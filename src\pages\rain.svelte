<script>
  import { onMount } from "svelte";

  let container;

  onMount(async () => {
    // Create a new image element
    const img = new Image();
    img.crossOrigin = "anonymous";
    img.src = "https://images.unsplash.com/photo-1506748686214-e9df14d4d9d0";

    // Wait for image to load
    await new Promise((resolve) => {
      img.onload = resolve;
    });

    // Create container div with background image
    container.style.backgroundImage = `url(${img.src})`;
    container.style.backgroundSize = "cover";
    container.style.backgroundPosition = "center center";

    // Initialize RainyDay
    // @ts-ignore - RainyDay is loaded from the global scope
    const rainyday = new RainyDay({
      image: img,
      blur: 10,
      opacity: 1,
      fps: 30,
      parentElement: container,
    });

    // Configure and add rain effect
    rainyday.rain([
      [3, 2, 2], // add 2 drops every frame, sizes from 3px
      [5, 4, 1], // add 1 drop every frame, sizes from 5px
      [6, 3, 1], // add 1 drop every frame, sizes from 6px
    ]);
  });
</script>

<main>
  <div class="glass-overlay" bind:this={container}></div>
</main>

<style>
  main {
    margin: 0;
    padding: 0;
    height: 100%;
    overflow: hidden;
    font-family: sans-serif;
  }

  .glass-overlay {
    position: relative;
    width: 100vw;
    height: 100vh;
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.05);
  }
</style>
