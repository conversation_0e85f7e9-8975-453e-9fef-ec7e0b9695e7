<script>
  import Masonry from "svelte-bricks";
  import { onMount } from "svelte";
  import { fade } from "svelte/transition";
  import VideoPlayer from "../components/VideoPlayer.svelte";

  let videoModalOpen = false;
  let videoRef = null;
  let cardPosition = { top: 0, left: 0, width: 0, height: 0 };

  // Track loading state for each video
  $: loadingStates = {};

  function setVideoLoaded(id) {
    loadingStates[id] = false;
  }

  let items = [
    { type: "video", src: "/gallery/word-sequence.mp4" },
    { type: "video", src: "/gallery/suika-arena.mov" },
    // { type: "image", src: "/gallery/hexa.webp" },
    { type: "video", src: "/gallery/Parlour.mp4" },
    { type: "video", src: "/gallery/Drift_IO_GameSection.mp4" },
    { type: "video", src: "/gallery/hexa-io.mp4" },
    { type: "video", src: "/gallery/GermanSheperd2_Forest_Win_TPS.mp4" },
    { type: "video", src: "/gallery/bunny-planet-runner.mp4" },
    { type: "video", src: "/gallery/doggy-run.mp4" },
    { type: "video", src: "/gallery/word-trivia.mp4" },
    { type: "video", src: "/gallery/TargetMaster.mp4" },
    // { type: "video", src: "/gallery/pet-sim-cat.mp4" },
    { type: "video", src: "/gallery/playzap.mp4" },
    { type: "video", src: "/gallery/pet-sim-dog.mp4" },
    { type: "video", src: "/gallery/fluid-sim.mp4" },
    { type: "video", src: "/gallery/game-select-playzap.mp4" },
    { type: "video", src: "/gallery/BlackJack1.mp4" },
    { type: "video", src: "/gallery/HiLo1.mp4" },
    { type: "video", src: "/gallery/Carscape_Ipad_Forest.mov" },
    { type: "video", src: "/gallery/Carscape_Ipad_Desert.mov" },
    { type: "video", src: "/gallery/Carscape_Ipad_Ice.mov" },
    { type: "video", src: "/gallery/snake-game.mp4" },
    { type: "image", src: "/gallery/playzap-world.jfif" },
    // { type: "video", src: "/gallery/INSTA _REEL_Full_Res.mp4" },
  ].map((item, index) => ({ ...item, id: index + 1 }));

  let [minColWidth, maxColWidth, gap] = [250, 800, 15];
  let width, height;

  // Event handlers for video elements
  function handleVideoMetadata(event) {
    const video = event.target;
    if (video instanceof HTMLVideoElement) {
      // video.play();

      // Get the video's ID from its data attribute
      const videoId = video.dataset.id;
      if (videoId) {
        setVideoLoaded(videoId);
      }
    }
  }

  function handleCanPlay(event) {
    const video = event.target;
    if (video instanceof HTMLVideoElement) {
      video.play();
      // Get the video's ID from its data attribute
      const videoId = video.dataset.id;
      if (videoId) {
        setVideoLoaded(videoId);
      }
    }
  }

  function handleWaiting(event) {
    const video = event.target;
    if (video instanceof HTMLVideoElement) {
      video.dataset.buffering = "true";
    }
  }

  function handlePlaying(event) {
    const video = event.target;
    if (video instanceof HTMLVideoElement) {
      video.dataset.buffering = "false";
    }
  }

  // Function to update buffer progress indicators
  // function updateBufferProgress() {
  //   const videos = document.querySelectorAll("video");

  //   videos.forEach((video) => {
  //     if (video.buffered.length > 0) {
  //       const bufferedEnd = video.buffered.end(video.buffered.length - 1);
  //       const duration = video.duration;
  //       const progress = (bufferedEnd / duration) * 100;

  //       const indicator = video.nextElementSibling;
  //       if (indicator instanceof HTMLElement) {
  //         indicator.style.width = `${progress}%`;
  //       }
  //     }
  //   });
  // }

  onMount(() => {
    // Initialize all videos as loading
    items.forEach((item) => {
      if (item.type === "video") {
        loadingStates[item.id] = true;
      }
    });
  });
</script>

<!-- Masonry size: <span>{width}px</span> &times; <span>{height}px</span> (w &times;h) -->

<main>
  <Masonry
    {items}
    {minColWidth}
    {maxColWidth}
    {gap}
    let:item
    bind:masonryWidth={width}
    bind:masonryHeight={height}
  >
    {#if item.type === "video"}
      <div class="video-container relative">
        <video
          class="rounded-lg"
          src={item.src}
          preload="metadata"
          loop
          muted
          playsinline
          controlslist="nodownload noremoteplayback"
          style="width: 100%; height: auto"
          data-id={item.id}
          on:loadedmetadata={handleVideoMetadata}
          on:canplay={handleCanPlay}
          on:waiting={handleWaiting}
          on:playing={handlePlaying}
          on:click={(event) => {
            videoModalOpen = true;
            const card = event.currentTarget.getBoundingClientRect();
            cardPosition = {
              top: card.top,
              left: card.left,
              width: 0,
              height: 0,
            };

            event.stopPropagation();
            videoRef = item.src;
          }}
        ></video>
        {#if loadingStates[item.id]}
          <div class="loading-throbber" transition:fade={{ duration: 300 }}>
            <div class="spinner"></div>
          </div>
        {/if}
      </div>
      <!-- <div class="bg-blue-500 w-full">
      </div> -->
    {:else}
      <img class="rounded-lg" src={item.src} alt="" />
      <!-- <div class="bg-blue-500 w-full aspect-square">
      </div> -->
    {/if}
  </Masonry>
</main>

{#if videoModalOpen && videoRef}
  <VideoPlayer
    url={videoRef}
    bind:modalOpen={videoModalOpen}
    bind:cardPosition
  />
{/if}

<style>
  main {
    padding: 2rem;
  }

  .video-container {
    position: relative;
    overflow: hidden;
  }

  .loading-throbber {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 0.5rem;
    z-index: 10;
    backdrop-filter: blur(2px);
  }

  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  /* .buffer-indicator {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background-color: #3b82f6;
    width: 0%;
    transition: width 0.3s ease;
  }

  video[data-buffering="true"] + .buffer-indicator {
    animation: buffering-pulse 1.5s infinite;
  }

  @keyframes buffering-pulse {
    0% {
      opacity: 0.6;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.6;
    }
  } */

  /* video,
  img {
    transform: scale(1);
    transition: transform 0.2s ease;
  }
  video:hover,
  img:hover {
    transform: scale(1.2);
  } */
</style>
