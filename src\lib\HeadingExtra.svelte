<script>
  const greekAlphabet = "-ΛΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΤΥΦΧΨΩ";
  // const greekAlphabet = "-日月金木水火土竹戈十大中一弓人心手口尸廿山女田難卜Ｚ";
  const englishAlphabet = "-ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const letters = "-ΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΤΥΦΧΨΩ";
  // const letters = 'АБВГДЕЁЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯ';
  // const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  // const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()-_=+[]{}|;:\'",.<>?/`~';

  export let defaultText = `PROJECT-X`;
  let text = defaultText;

  let interval = null;

  text = defaultText;
  startAnimation();

  function startAnimation() {
    let iteration = 0;

    clearInterval(interval);

    interval = setInterval(() => {
      text = text
        .split("")
        .map((letter, index) => {
          if (letter == " ") return " ";
          if (index < iteration) {
            return greekAlphabet[englishAlphabet.indexOf(defaultText[index])];
            // return text[index];
          }

          return greekAlphabet[
            Math.floor(Math.random() * greekAlphabet.length)
          ];
        })
        .join("");

      if (iteration >= defaultText.length) {
        clearInterval(interval);
      }

      iteration += 1 / 3;
    }, 30);
  }

  function revertAnimation() {
    let iteration = defaultText.length - 1;

    clearInterval(interval);

    interval = setInterval(() => {
      text = text
        .split("")
        .map((letter, index) => {
          if (letter == " ") return letter;
          if (index > iteration) {
            return defaultText[index];
            // return englishAlphabet[greekAlphabet.indexOf(letter)];
          }

          return greekAlphabet[
            Math.floor(Math.random() * greekAlphabet.length)
          ];
        })
        .join("");

      if (iteration < 0) {
        clearInterval(interval);
      }

      iteration -= 1 / 3;
    }, 30);
  }
</script>

<div
  class="text"
  on:pointerover={revertAnimation}
  on:pointerleave={startAnimation}
>
  {text}
</div>

<style>
  .text {
    width: max-content;
    max-width: 100%;
    overflow-wrap: break-word;

    font-family: "Space Mono", monospace;
    /* More balanced clamp with better minimum size and smoother scaling */
    font-size: clamp(1.25rem, 5vw + 0.5rem, 4rem);
    letter-spacing: clamp(0.05em, 0.1vw, 0.15em);
    line-height: 1.2;

    font-weight: 700;
    color: #ffffff;

    cursor: grabbing;
    transition:
      font-size 0.3s ease,
      letter-spacing 0.3s ease;
  }

  /* Additional breakpoints for more precise control */
  @media (max-width: 768px) {
    .text {
      font-size: clamp(1.25rem, 6vw + 0.5rem, 3.5rem);
      letter-spacing: 0.05em;
    }
  }

  @media (max-width: 480px) {
    .text {
      font-size: clamp(1.25rem, 7vw + 0.25rem, 3rem);
      letter-spacing: 0.025em;
    }
  }
</style>
