<script>
  export let color;
  export let tech;
  export let customClass = "";
  export let isActive = false;

  import Icon from "@iconify/svelte";
  import { createEventDispatcher } from "svelte";

  const dispatch = createEventDispatcher();

  function filterProjects(tech) {
    console.log(tech);
    dispatch("filter", tech);
  }

  function removeFilter(event) {
    event.stopPropagation(); // Prevent the button click event
    dispatch("remove");
  }
</script>

<button
  class="grayscale hover:grayscale-0 {customClass} {isActive
    ? 'active grayscale-0'
    : ''} flex items-center justify-around gap-2"
  style="--bg-color: {color};"
  on:click={() => filterProjects(tech)}
>
  {tech}
  {#if isActive && customClass !== "cardTag"}
    <!-- svelte-ignore a11y-click-events-have-key-events -->
    <span
      class="inline-flex items-center justify-center"
      role="button"
      tabindex="0"
      on:click={removeFilter}
      on:keydown={(e) => e.key === "Enter" && removeFilter(e)}
    >
      <Icon height="15" width="15" icon="mdi:close" />
    </span>
  {/if}
</button>

<style>
  button {
    background-color: var(--bg-color);
    padding: 0.5rem 1rem;
    color: white;
    cursor: pointer;
    box-shadow: 2px 4px 5px rgba(0, 0, 0, 0.5);
    transition: box-shadow 0.3s ease;
  }
  button:hover {
    box-shadow: 0px 0px 20px var(--bg-color);
  }

  .cardTag {
    box-shadow: none;
    font-size: 0.8em;
    padding: 0.2rem 0.5rem;
    border-radius: 5px;
  }
</style>
