<script>
  import Icon from "@iconify/svelte";
  import emailjs from "@emailjs/browser";

  let name = "";
  let email = "";
  let subject = "";
  let message = "";
  let isSubmitting = false;
  let submitSuccess = false;
  let submitError = false;
  let errorMessage = "";

  // Form validation
  $: nameValid = name.length > 0;
  $: emailValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  $: subjectValid = subject.length > 0;
  $: messageValid = message.length > 0;
  $: formValid = nameValid && emailValid && subjectValid && messageValid;

  async function handleSubmit() {
    if (!formValid) return;

    isSubmitting = true;
    submitSuccess = false;
    submitError = false;

    try {
      await emailjs.send(
        "portfolio", //SERVICE ID
        "template_yys7srf", //TEMPLATE ID
        {
          title: subject,
          name: name,
          time: new Date().toLocaleTimeString(),
          message: message,
          from_name: name,
          reply_to: email,
        },
        "bimoieCJuDwX5jWIV" //PUBLIC KEY
      );

      submitSuccess = true;
      name = "";
      email = "";
      subject = "";
      message = "";
    } catch (error) {
      // Error
      submitError = true;
      errorMessage =
        "There was an error sending your message. Please try again.";
    } finally {
      isSubmitting = false;
    }
  }
</script>

<div class="container">
  <div class="contact-container">
    <div class="contact-header">
      <h1 class="text-3xl font-bold mb-6">Get In Touch</h1>
      <p class="text-lg mb-8">
        Have a project in mind or want to collaborate? Feel free to reach out!
      </p>
    </div>

    <div class="contact-content">
      <div class="contact-form-container">
        <form on:submit|preventDefault={handleSubmit} class="contact-form">
          <div class="form-group">
            <label for="name">Name</label>
            <input
              type="text"
              id="name"
              bind:value={name}
              class:invalid={!nameValid && name !== ""}
              placeholder="Your name"
              required
            />
          </div>

          <div class="form-group">
            <label for="email">Email</label>
            <input
              type="email"
              id="email"
              bind:value={email}
              class:invalid={!emailValid && email !== ""}
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div class="form-group">
            <label for="subject">Subject</label>
            <input
              type="text"
              id="subject"
              bind:value={subject}
              class:invalid={!subjectValid && subject !== ""}
              placeholder="What's this about?"
              required
            />
          </div>

          <div class="form-group">
            <label for="message">Message</label>
            <textarea
              id="message"
              bind:value={message}
              class:invalid={!messageValid && message !== ""}
              placeholder="Your message here..."
              rows="5"
              required
            ></textarea>
          </div>

          <button
            type="submit"
            class="submit-button"
            disabled={!formValid || isSubmitting}
          >
            {#if isSubmitting}
              Sending...
            {:else}
              Send Message
            {/if}
          </button>

          {#if submitSuccess}
            <div class="success-message">
              Thank you! Your message has been sent successfully.
            </div>
          {/if}

          {#if submitError}
            <div class="error-message">
              {errorMessage}
            </div>
          {/if}
        </form>
      </div>

      <div class="contact-info">
        <div class="info-section">
          <h2 class="text-xl font-bold mb-4">Contact Information</h2>
          <div class="info-item">
            <span class="info-label">Email:</span>
            <a href="mailto:<EMAIL>" class="info-value"
              ><EMAIL></a
            >
          </div>
          <div class="info-item">
            <span class="info-label">Location:</span>
            <span class="info-value">India</span>
          </div>
        </div>

        <div class="info-section">
          <h2 class="text-xl font-bold mb-4">Connect With Me</h2>
          <div class="social-links">
            <a
              href="https://github.com/hunny2000"
              target="_blank"
              rel="noopener noreferrer"
              class="social-link"
            >
              <Icon height="20" width="20" icon="mdi:github" />
              <span>GitHub</span>
            </a>
            <a
              href="https://www.linkedin.com/in/abhinav-bhasin/"
              target="_blank"
              rel="noopener noreferrer"
              class="social-link"
            >
              <Icon height="20" width="20" icon="mdi:linkedin" />
              <span>LinkedIn</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .container {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
  }

  .contact-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .contact-header {
    text-align: center;
  }

  .contact-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  @media (min-width: 768px) {
    .contact-content {
      flex-direction: row;
    }

    .contact-form-container {
      flex: 3;
    }

    .contact-info {
      flex: 2;
    }
  }

  .contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    background-color: var(--secondary-background-color);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.2);
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  label {
    font-weight: 600;
    color: var(--text--v1-2);
  }

  input,
  textarea {
    padding: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    background-color: rgba(0, 0, 0, 0.2);
    color: white;
    font-size: 1rem;
    transition:
      border-color 0.3s,
      box-shadow 0.3s;
  }

  input:focus,
  textarea:focus {
    outline: none;
    border-color: var(--text--v1-2);
    box-shadow: 0 0 0 2px rgba(136, 185, 228, 0.3);
  }

  input.invalid,
  textarea.invalid {
    border-color: #e53e3e;
  }

  .submit-button {
    padding: 0.75rem 1.5rem;
    background-color: var(--text--v1-2);
    color: white;
    border: none;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition:
      background-color 0.3s,
      transform 0.2s;
  }

  .submit-button:hover:not(:disabled) {
    background-color: #6da8d8;
    transform: translateY(-2px);
  }

  .submit-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }

  .success-message {
    margin-top: 1rem;
    padding: 0.75rem;
    background-color: rgba(72, 187, 120, 0.2);
    color: #48bb78;
    border-radius: 5px;
    text-align: center;
  }

  .error-message {
    margin-top: 1rem;
    padding: 0.75rem;
    background-color: rgba(229, 62, 62, 0.2);
    color: #e53e3e;
    border-radius: 5px;
    text-align: center;
  }

  .contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .info-section {
    background-color: var(--secondary-background-color);
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.2);
  }

  .info-item {
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .info-label {
    font-weight: 600;
    color: var(--text--v1-2);
  }

  .info-value {
    color: white;
  }

  .social-links {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .social-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border-radius: 5px;
    text-decoration: none;
    color: white;
    transition:
      background-color 0.3s,
      transform 0.2s;
  }

  .social-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
  }
</style>
