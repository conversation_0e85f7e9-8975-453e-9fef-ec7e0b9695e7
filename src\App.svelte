<script>
  // import { Router, Route, Link } from "svelte-routing";

  import Router from "svelte-spa-router";
  // import NotFound from "./pages/NotFound.svelte";

  import Footer from "./components/Footer.svelte";
  import HeadingExtra from "./lib/HeadingExtra.svelte";
  import Toggle from "./lib/Toggle.svelte";

  import Projects from "./pages/Projects.svelte";
  import Home from "./pages/Home.svelte";
  import test from "./pages/test.svelte";
  import Testday from "./pages/testday.svelte";
  import Gallery from "./pages/Gallery.svelte";
  import Contact from "./pages/Contact.svelte";
  import NotFound from "./pages/NotFound.svelte";
  import Navigation from "./components/Navigation.svelte";
  import Roomtest from "./pages/roomtest.svelte";
  import Rain from "./pages/rain.svelte";

  // let themeToggle = localStorage.getItem("theme") === "light" ? false : true;
  // let theme = themeToggle ? "dark" : "light";

  // // Reactive statement to handle theme changes
  // $: {
  //   console.log(themeToggle);
  //   theme = themeToggle ? "dark" : "light";
  //   document.documentElement.setAttribute("data-theme", theme);
  //   localStorage.setItem("theme", theme);
  // }

  const routes = {
    // Exact path
    "/": Home,
    "/room": Roomtest,
    "/hero": test,
    "/test": Testday,
    "/rain": Rain,
    "/projects": Projects,
    "/gallery": Gallery,
    "/contact": Contact,
    // Catch-all
    // This is optional, but if present it must be the last
    "*": NotFound,
  };

  const navItems = [
    { name: "Home", href: "/#" },
    // { name: "Hero", href: "/#/hero" },
    // { name: "Test", href: "/#/test" },
    // { name: "Room", href: "/#/room" },
    // { name: "Rain", href: "/#/rain" },
    { name: "Projects", href: "/#/projects" },
    { name: "Gallery", href: "/#/gallery" },
    { name: "Contact", href: "/#/contact" },
  ];
</script>

<main>
  <!-- <h1>Abhinav Bhasin</h1> -->
  <div class="Header">
    <HeadingExtra defaultText="ABHINAV BHASIN" />
    <Navigation items={navItems} />
    <!-- <Toggle /> -->
  </div>

  <div class="container">
    <Router {routes} />
  </div>
  <!-- <Router useHash={true}>
    <Route path="/" component={Home} />
    <Route path="/projects" component={Projects} />
    <Route path="/gallery" component={Gallery} />
    <Route path="/contact" component={Contact} />
  </Router> -->

  <Footer />
</main>

<style>
  .Header {
    height: 150px;

    display: flex;
    justify-content: space-between;
    align-items: center;

    margin-left: 2rem;
    margin-right: 2rem;
  }

  .container {
    /* min-height: 100dvh; */
    min-width: 100dvw;
  }

  @media (max-width: 768px) {
    .Header {
      height: 12vh;
    }
  }
</style>
