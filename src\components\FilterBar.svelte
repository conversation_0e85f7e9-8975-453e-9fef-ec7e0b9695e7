<script>
  import { techColors } from "../lib/util";
  import FilterButton from "./FilterButton.svelte";

  export let handleFilter;
  export let removeFilter;
  export let activeFilters = [];

  // Show all filters if no active filters
  $: displayFilters =
    activeFilters.length === 0 ? Object.keys(techColors) : activeFilters;
</script>

<!-- {#if activeFilters.length !== 0}
  <div class="py-5 flex items-center justify-center">
    <div class="flex justify-center items-center flex-wrap gap-2.5">
      {#each displayFilters as tech}
        <FilterButton
          on:filter={handleFilter}
          {tech}
          color={techColors[tech]}
          isActive={true}
          on:remove={() => removeFilter(tech)}
        />
      {/each}
    </div>
  </div>
{/if} -->

<div class="flex items-center justify-center flex-wrap px-4 py-6 gap-2">
  <!-- <span class="filter-label">All Filters:</span> -->
  {#each Object.entries(techColors) as [tech, color]}
    <FilterButton
      on:filter={handleFilter}
      {tech}
      {color}
      isActive={activeFilters.includes(tech)}
      on:remove={() => removeFilter(tech)}
    />
  {/each}

  <!-- {#if activeFilters.length > 0}
    <span
      class="filter-label"
      role="button"
      tabindex="0"
      on:click={() => {
        activeFilters.forEach((tech) => {
          removeFilter(tech);
        });
        activeFilters = [];
      }}
    >
      clear all
    </span>
  {/if} -->
</div>
