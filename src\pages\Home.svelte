<script>
  // export let params;
  import Hero from "../assets/hero.webp";
  import Contact from "./Contact.svelte";
  import Projects from "./Projects.svelte";
</script>

<div class="hero">
  <img src={Hero} alt="" />
  <p>
    <span>
      Hi, I'm
      <span class="text-blue-400 text-xl"> Abhinav </span>
      a.k.a.
      <span class="text-blue-400 text-xl"> MINITITAN</span>
    </span>
    <br />
    <br />
    I'm a full stack game developer from

    <span class="text-orange-500">I N</span>
    <span class="text-white">D I</span>
    <span class="text-green-500">A</span>
    <!-- India. -->
    <br />
    <br />
    I thrive in roles that offer a mix of technical and creative challenges. With
    a strong background in game development, I'm comfortable taking on leadership
    roles or contributing as a specialist. I'm adaptable and willing to take on diverse
    responsibilities to ensure the project's success.
    <br />
    <br />
    I have a strong passion for game development, having worked on a diverse range
    of projects including
    <span class="text-blue-400">hyper-casual</span>,
    <span class="text-orange-500">casual</span>,
    <span class="text-green-500">UI/UX design</span>,
    <span class="text-yellow-500">casino style minigames</span>,
    <span class="text-red-500">educational</span>,
    <span class="text-purple-500">puzzle</span>, and
    <span class="text-pink-500">action-packed 3D experiences</span>.
    <br />
    <br />
    Currently, I am working as a game developer at PlayZap Games, a free to play
    crypto gaming app. Prior to this, I have worked in a startup and a large educational
    company, and I also enjoy participating in game jams as a hobby.
    <br />
    <br />
    My skills include gameplay programming, procedural generation, shader programming,
    content generation, designer tools, and backend programming.
    <br />
    <br />
    <!-- <span>You can find me here:</span>
    <span class="socials">
      <a href="https://github.com/abhinavbhasin">Github</a>
      <a href="https://www.linkedin.com/in/abhinavbhasin/">Linkedin</a>
      <a href="https://twitter.com/abhinavbhasin">Twitter</a>
      <a href="https://dev.to/abhinavbhasin">Dev.to</a>
      <a href="https://medium.com/@abhinavbhasin">Medium</a>
      <a href="https://abhinavbhasin.hashnode.dev">Hashnode</a>
      <a href="https://www.instagram.com/abhinavbhasin/">Instagram</a>
      <a href="https://www.facebook.com/abhinavbhasin">Facebook</a>
      <a href="https://www.youtube.com/channel/UCXb5ZBZz7WZq8yfWwP2Gd0w"
        >Youtube</a
      >
      <a href="https://t.me/abhinavbhasin">Telegram</a>
      <a href="https://www.patreon.com/abhinavbhasin">Patreon</a>
      <a href="https://ko-fi.com/abhinavbhasin">Ko-fi</a>
      <a href="https://www.buymeacoffee.com/abhinavbhasin">Buy me a coffee</a>
      <a href="https://www.paypal.me/abhinavbhasin">Paypal</a>
      <a href="https://www.buymeacoffee.com/abhinavbhasin">Buy me a coffee</a>
    </span> -->
  </p>
</div>

<Projects />
<Contact />

<style>
  .hero {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 2rem;
    padding: 2rem;
    background-color: rgba(255, 255, 255, 0.1);
    color: #9b9b9b;
  }
  .socials {
    display: flex;
    /* flex-direction: row; */
    /* align-items: center; */
    /* justify-content: center; */
    column-gap: 2rem;
    flex-wrap: wrap;
  }
  img {
    height: 60vh;
    transform: scale(1);
    filter: grayscale(100%);
    transition:
      transform 0.5s ease,
      filter 1s ease,
      box-shadow 0.2s ease;
  }
  img:hover {
    transform: scale(1.02);
    filter: grayscale(0%);
    box-shadow: 2px 4px 2px rgba(0, 0, 0, 0.2);
  }

  @media (max-width: 1024px) {
    .hero {
      flex-direction: column;
      padding: 1rem;
    }
    img {
      height: 50vh;
    }
    p {
      font-size: 0.9rem;
    }
  }
  @media (max-width: 768px) {
    .hero {
      flex-direction: column;
      padding: 1rem;
    }
    img {
      height: 50vh;
    }
    p {
      font-size: 0.8rem;
    }
  }
</style>
