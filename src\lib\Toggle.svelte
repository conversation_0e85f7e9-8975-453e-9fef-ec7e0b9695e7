<script>
  import { onMount } from "svelte";
  // import { faSun, faMoon } from "@fortawesome/free-solid-svg-icons";
  // import { Fa } from "svelte-fa";
  // import Icon from "@iconify/svelte";

  let value = false;

  let theme = localStorage.getItem("theme") || "dark";

  const toggleTheme = () => {
    theme = theme === "light" ? "dark" : "light";
    document.documentElement.setAttribute("data-theme", theme);
    localStorage.setItem("theme", theme);
  };

  onMount(() => {
    document.documentElement.setAttribute("data-theme", theme);
  });
</script>

<!-- <div class="toggle-wrapper">
  <div class="toggle {theme}" on:click={toggleTheme}>
    <div class="switch">
      {#if theme === "light"}
        <Fa icon={faSun} />
      {:else}
        <Fa icon={faMoon} />
      {/if}
    </div>
  </div>
</div> -->

<div class="toggle-wrapper">
  <button
    class="toggle {theme}"
    role="switch"
    aria-checked={value}
    on:click={toggleTheme}
  >
    <div class="switch">
      {#if theme === "light"}
        <!-- <Fa icon={faSun} /> -->
        <!-- <Icon icon="mdi:weather-night" /> -->
        <Icon height="20" width="20" icon="material-symbols:logout" />
      {:else}
        <!-- <Fa icon={faMoon} /> -->
      {/if}
    </div>
  </button>
</div>

<style>
  .toggle-wrapper {
    position: fixed;
    bottom: 10px;
    left: 15px;

    display: flex;
    align-items: center;
    justify-content: flex-end;

    z-index: 99;
  }

  .toggle {
    position: relative;

    width: 55px;
    height: 30px;

    background: var(--color-bg-secondary);
    border-radius: 25px;
    cursor: pointer;

    transition: background 0.3s;
  }

  .toggle .switch {
    position: absolute;
    top: -3px;

    width: 35px;
    height: 35px;

    background: var(--color-text);
    border-radius: 50%;

    transition: left 0.3s;

    display: flex;
    align-items: center;
    justify-content: center;
  }

  .toggle.light .switch {
    left: -8px;
    color: #ffffff;
  }

  .toggle.dark .switch {
    left: 26px;
    color: #0a0a0a;
  }

  .icon {
    margin-left: 10px;
  }
</style>
