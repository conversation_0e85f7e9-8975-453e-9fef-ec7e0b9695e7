@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;

  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: var(--text--v1-2);
  background-color: var(--primary-background-color);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  --primary-background-color: #242424;
  --secondary-background-color: #3b3b3b;

  --ui-t--v-1: #ffffff;
  --hig-l--v1: #90e33f;
  --text--v1-2: #88b9e4;
  --on--w--short-dark: 0px 5px 5px rgba(0, 0, 0, 0.5);

  /* --color-bg-primary: #ffffff;
  --color-bg-secondary: #e3e4e5;
  --color-text: #213547;
  --color-link: #646cff;
  --color-link-hover: #747bff; */
  --color-bg-primary: #242424;
  --color-bg-secondary: #303030;
  --color-text: rgba(255, 255, 255, 0.87);
  --color-link: #646cff;
  --color-link-hover: #535bf2;
}

/* [data-theme="dark"] {
} */

body,
main {
  margin: 0;
  padding: 0;

  width: 100vw;
  /* display: flex; */
  /* place-items: center; */
  min-width: 320px;
  min-height: 100vh;

  overflow-x: hidden;
}

#app {
  /* max-width: 1280px; */
  margin: 0 auto;
  min-height: 100vh;
  /* padding: 2rem; */
  /* text-align: center; */
}

button {
  border: none;
  border-radius: 8px;
  /* border: 1px solid transparent; */

  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  cursor: pointer;
}
/* For Webkit-based browsers (Chrome, Safari and Opera) */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* For IE, Edge and Firefox */
.scrollbar-hide {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}
/* 
@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
} */
