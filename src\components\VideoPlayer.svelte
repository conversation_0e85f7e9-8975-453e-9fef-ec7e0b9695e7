<script>
  // @ts-nocheck

  import { fade, fly, scale } from "svelte/transition";
  import { cubicOut, cubicIn } from "svelte/easing";

  export let url;
  export let modalOpen;

  export let cardPosition = { top: 0, left: 0, width: 0, height: 0 };

  let ModalStyles = `top: ${cardPosition.top}px; left: ${cardPosition.left}px; width: ${cardPosition.width}px; height: ${cardPosition.height}px;`;
  function getModalStyles(cardPosition) {
    return `top: ${cardPosition.top}px; left: ${cardPosition.left}px; width: ${cardPosition.width}px; height: ${cardPosition.height}px;`;
  }

  $: if (modalOpen) {
    setTimeout(() => {
      ModalStyles = "";
    }, 10);
  }
</script>

<!-- <div class="Container">
  <video src={url}></video>
</div> -->

<div class="backdrop" transition:fade></div>
<!-- svelte-ignore missing-declaration -->
<!-- svelte-ignore a11y-click-events-have-key-events -->
<!-- svelte-ignore a11y-no-static-element-interactions -->
<div
  class="Container"
  transition:fly={{ y: 0, duration: 500, easing: cubicOut }}
  style={ModalStyles}
  on:click={() => {
    ModalStyles = getModalStyles(cardPosition);
    setTimeout(() => {
      modalOpen = false;
    }, 10);
  }}
>
  <!-- svelte-ignore a11y-media-has-caption -->
  <video
    src={url}
    loop
    muted
    controls
    playsinline
    preload="metadata"
    controlslist="nodownload noremoteplayback"
    volume="0.1"
    autoplay
    on:click|stopPropagation
  ></video>
</div>

<style>
  .Container {
    position: fixed;
    top: 50%;
    left: 0;

    transform: translate(0, -50%);

    width: 100%;
    height: 100%;

    /* object-fit: cover; */

    z-index: 100;

    /* border-radius: 20px; */
    /* box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); */

    display: flex;
    justify-content: center;
    align-items: center;

    transition: all 1.3s ease;
    /* overflow: hidden; */
  }

  .backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 99;
    transition: all 1.3s ease;
  }

  video {
    z-index: 101;
    /* width: 90%; */
    /* height: 90%; */
    max-height: 90%;

    border-radius: 30px;
    /* border: 5px solid rgba(0, 0, 0, 0.8); */
    box-shadow: 0px 0px 10px 1px rgba(0, 0, 0, 0.8);
    /* margin: 40px; */
  }
</style>
