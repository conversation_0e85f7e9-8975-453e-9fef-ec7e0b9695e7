(function(){const S=document.createElement("link").relList;if(S&&S.supports&&S.supports("modulepreload"))return;for(const e of document.querySelectorAll('link[rel="modulepreload"]'))a(e);new MutationObserver(e=>{for(const s of e)if(s.type==="childList")for(const c of s.addedNodes)c.tagName==="LINK"&&c.rel==="modulepreload"&&a(c)}).observe(document,{childList:!0,subtree:!0});function g(e){const s={};return e.integrity&&(s.integrity=e.integrity),e.referrerPolicy&&(s.referrerPolicy=e.referrerPolicy),e.crossOrigin==="use-credentials"?s.credentials="include":e.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function a(e){if(e.ep)return;e.ep=!0;const s=g(e);fetch(e.href,s)}})();var te;(function(f){var S=function(){function a(){this._dropEffect="move",this._effectAllowed="all",this._data={}}return Object.defineProperty(a.prototype,"dropEffect",{get:function(){return this._dropEffect},set:function(e){this._dropEffect=e},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"effectAllowed",{get:function(){return this._effectAllowed},set:function(e){this._effectAllowed=e},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"types",{get:function(){return Object.keys(this._data)},enumerable:!0,configurable:!0}),a.prototype.clearData=function(e){e!=null?delete this._data[e]:this._data=null},a.prototype.getData=function(e){return this._data[e]||""},a.prototype.setData=function(e,s){this._data[e]=s},a.prototype.setDragImage=function(e,s,c){var p=g._instance;p._imgCustom=e,p._imgOffset={x:s,y:c}},a}();f.DataTransfer=S;var g=function(){function a(){if(this._lastClick=0,a._instance)throw"DragDropTouch instance already created.";var e=!1;if(document.addEventListener("test",function(){},{get passive(){return e=!0,!0}}),"ontouchstart"in document){var s=document,c=this._touchstart.bind(this),p=this._touchmove.bind(this),y=this._touchend.bind(this),h=e?{passive:!1,capture:!1}:!1;s.addEventListener("touchstart",c,h),s.addEventListener("touchmove",p,h),s.addEventListener("touchend",y),s.addEventListener("touchcancel",y)}}return a.getInstance=function(){return a._instance},a.prototype._touchstart=function(e){var s=this;if(this._shouldHandle(e)){if(Date.now()-this._lastClick<a._DBLCLICK&&this._dispatchEvent(e,"dblclick",e.target)){e.cancelable&&e.preventDefault(),this._reset();return}this._reset();var c=this._closestDraggable(e.target);c&&!this._dispatchEvent(e,"mousemove",e.target)&&!this._dispatchEvent(e,"mousedown",e.target)&&(this._dragSource=c,this._ptDown=this._getPoint(e),this._lastTouch=e,e.cancelable&&e.preventDefault(),setTimeout(function(){s._dragSource==c&&s._img==null&&s._dispatchEvent(e,"contextmenu",c)&&s._reset()},a._CTXMENU),a._ISPRESSHOLDMODE&&(this._pressHoldInterval=setTimeout(function(){s._isDragEnabled=!0,s._touchmove(e)},a._PRESSHOLDAWAIT)))}},a.prototype._touchmove=function(e){if(this._shouldCancelPressHoldMove(e)){this._reset();return}if(this._shouldHandleMove(e)||this._shouldHandlePressHoldMove(e)){var s=this._getTarget(e);if(this._dispatchEvent(e,"mousemove",s)){this._lastTouch=e,e.cancelable&&e.preventDefault();return}this._dragSource&&!this._img&&this._shouldStartDragging(e)&&(this._dispatchEvent(e,"dragstart",this._dragSource),this._createImage(e),this._dispatchEvent(e,"dragenter",s)),this._img&&(this._lastTouch=e,e.cancelable&&e.preventDefault(),s!=this._lastTarget&&(this._dispatchEvent(this._lastTouch,"dragleave",this._lastTarget),this._dispatchEvent(e,"dragenter",s),this._lastTarget=s),this._moveImage(e),this._isDropZone=this._dispatchEvent(e,"dragover",s))}},a.prototype._touchend=function(e){if(this._shouldHandle(e)){if(this._dispatchEvent(this._lastTouch,"mouseup",e.target)){e.cancelable&&e.preventDefault();return}this._img||(this._dragSource=null,this._dispatchEvent(this._lastTouch,"click",e.target),this._lastClick=Date.now()),this._destroyImage(),this._dragSource&&(e.type.indexOf("cancel")<0&&this._isDropZone&&this._dispatchEvent(this._lastTouch,"drop",this._lastTarget),this._dispatchEvent(this._lastTouch,"dragend",this._dragSource),this._reset())}},a.prototype._shouldHandle=function(e){return e&&!e.defaultPrevented&&e.touches&&e.touches.length<2},a.prototype._shouldHandleMove=function(e){return!a._ISPRESSHOLDMODE&&this._shouldHandle(e)},a.prototype._shouldHandlePressHoldMove=function(e){return a._ISPRESSHOLDMODE&&this._isDragEnabled&&e&&e.touches&&e.touches.length},a.prototype._shouldCancelPressHoldMove=function(e){return a._ISPRESSHOLDMODE&&!this._isDragEnabled&&this._getDelta(e)>a._PRESSHOLDMARGIN},a.prototype._shouldStartDragging=function(e){var s=this._getDelta(e);return s>a._THRESHOLD||a._ISPRESSHOLDMODE&&s>=a._PRESSHOLDTHRESHOLD},a.prototype._reset=function(){this._destroyImage(),this._dragSource=null,this._lastTouch=null,this._lastTarget=null,this._ptDown=null,this._isDragEnabled=!1,this._isDropZone=!1,this._dataTransfer=new S,clearInterval(this._pressHoldInterval)},a.prototype._getPoint=function(e,s){return e&&e.touches&&(e=e.touches[0]),{x:s?e.pageX:e.clientX,y:s?e.pageY:e.clientY}},a.prototype._getDelta=function(e){if(a._ISPRESSHOLDMODE&&!this._ptDown)return 0;var s=this._getPoint(e);return Math.abs(s.x-this._ptDown.x)+Math.abs(s.y-this._ptDown.y)},a.prototype._getTarget=function(e){for(var s=this._getPoint(e),c=document.elementFromPoint(s.x,s.y);c&&getComputedStyle(c).pointerEvents=="none";)c=c.parentElement;return c},a.prototype._createImage=function(e){this._img&&this._destroyImage();var s=this._imgCustom||this._dragSource;if(this._img=s.cloneNode(!0),this._copyStyle(s,this._img),this._img.style.top=this._img.style.left="-9999px",!this._imgCustom){var c=s.getBoundingClientRect(),p=this._getPoint(e);this._imgOffset={x:p.x-c.left,y:p.y-c.top},this._img.style.opacity=a._OPACITY.toString()}this._moveImage(e),document.body.appendChild(this._img)},a.prototype._destroyImage=function(){this._img&&this._img.parentElement&&this._img.parentElement.removeChild(this._img),this._img=null,this._imgCustom=null},a.prototype._moveImage=function(e){var s=this;requestAnimationFrame(function(){if(s._img){var c=s._getPoint(e,!0),p=s._img.style;p.position="absolute",p.pointerEvents="none",p.zIndex="999999",p.left=Math.round(c.x-s._imgOffset.x)+"px",p.top=Math.round(c.y-s._imgOffset.y)+"px"}})},a.prototype._copyProps=function(e,s,c){for(var p=0;p<c.length;p++){var y=c[p];e[y]=s[y]}},a.prototype._copyStyle=function(e,s){if(a._rmvAtts.forEach(function($){s.removeAttribute($)}),e instanceof HTMLCanvasElement){var c=e,p=s;p.width=c.width,p.height=c.height,p.getContext("2d").drawImage(c,0,0)}for(var y=getComputedStyle(e),h=0;h<y.length;h++){var I=y[h];I.indexOf("transition")<0&&(s.style[I]=y[I])}s.style.pointerEvents="none";for(var h=0;h<e.children.length;h++)this._copyStyle(e.children[h],s.children[h])},a.prototype._dispatchEvent=function(e,s,c){if(e&&c){var p=document.createEvent("Event"),y=e.touches?e.touches[0]:e;return p.initEvent(s,!0,!0),p.button=0,p.which=p.buttons=1,this._copyProps(p,e,a._kbdProps),this._copyProps(p,y,a._ptProps),p.dataTransfer=this._dataTransfer,c.dispatchEvent(p),p.defaultPrevented}return!1},a.prototype._closestDraggable=function(e){for(;e;e=e.parentElement)if(e.hasAttribute("draggable")&&e.draggable)return e;return null},a}();g._instance=new g,g._THRESHOLD=5,g._OPACITY=.5,g._DBLCLICK=500,g._CTXMENU=900,g._ISPRESSHOLDMODE=!1,g._PRESSHOLDAWAIT=400,g._PRESSHOLDMARGIN=25,g._PRESSHOLDTHRESHOLD=0,g._rmvAtts="id,class,style,draggable".split(","),g._kbdProps="altKey,ctrlKey,metaKey,shiftKey".split(","),g._ptProps="pageX,pageY,clientX,clientY,screenX,screenY".split(","),f.DragDropTouch=g})(te||(te={}));const me=new URL("/assets/1-free-rNiAhJfN.png",import.meta.url).href,ye=new URL("/assets/2-free-CPOVU0ej.png",import.meta.url).href,oe=new URL("/assets/3-free-DCmZr4g8.png",import.meta.url).href,_e=new URL("/assets/50-pts-DyzImnlc.png",import.meta.url).href,ve=new URL("/assets/Analog-Nostalgia-4F0OEFGQ.mp3",import.meta.url).href,Ce=new URL("/assets/AUDIENCE_Claps_and_Cheers_02_stereo-CbYkGvrQ.mp3",import.meta.url).href,Se=new URL("/assets/start_new-D4pzXZKr.mp3",import.meta.url).href,Te=new URL("/assets/flip-CRXIvZAZ.ogg",import.meta.url).href,we=new URL("/assets/chime_bell_ding-Bx4ghufq.wav",import.meta.url).href,Le=new URL("/assets/bookFlip3-8UaRtDeA.ogg",import.meta.url).href,De=new URL("/assets/bookOpen-Bpu1qW-H.ogg",import.meta.url).href,Ee=new URL("/assets/drawKnife2-DOCfjG_q.ogg",import.meta.url).href,ke=new URL("/assets/bookFlip1-CXuoJ-tM.ogg",import.meta.url).href,be=new URL("/assets/handleSmallLeather2-PNhVWsP7.ogg",import.meta.url).href;audioAutodrop.src=be;audioDeckdrop.src=Ee;audioDraw.src=Le;audioCancel.src=De;audioReroll.src=ke;audioScoreFly.src=we;audioCardFlip.src=Te;audioWin.src=Ce;audioStartCards.src=Se;audioBG.src=ve;audioBG.loop=!0;audioBG.volume=.2;function ne(){window.removeEventListener("click",ne)}window.addEventListener("click",ne);function Oe(){let f=!1;var S=[],g=E.reroll,a=E.rewind;const e=document.querySelector(".undo > p");e.textContent=a;const s=document.querySelector(".deck_close"),c=document.querySelector(".deck_open");s.style.backgroundImage=`url("${oe}")`,g--;const p=document.querySelector(".undo");p.addEventListener("click",$),p.addEventListener("dragstart",o=>{o.preventDefault()});const y=document.querySelector(".submitnquit");y.addEventListener("click",()=>{v.send(JSON.stringify({type:"stopTimer",gameType:"casual",game:"solitaire",payload:{accessToken:`Bearer ${C}`,id:_}}))}),y.addEventListener("dragstart",o=>{o.preventDefault()});function h(o,n,t){console.log("push with deck: ",o,t,n),S.push({type:o,card:n!==void 0?n:null,destinationDeck:t!==void 0?t:null})}let I=!1;function $(){I||(I=!0,setTimeout(()=>{I=!1},1e3),v.send(JSON.stringify({type:"popHistory",gameType:"casual",game:"solitaire",payload:{accessToken:`Bearer ${C}`,id:_}})))}this.popHistory=function(){if(console.log("popHistory"),!!S.length){var o=S.pop();if(e.textContent=E.rewind,o.type=="move"){console.log("undoing history move: "+o.type);const t=[...o.card.parentElement.children],i=t.indexOf(o.card),l=t.splice(i),d=o.destinationDeck.lastElementChild;l.forEach((r,u)=>setTimeout(()=>{P(r,o.destinationDeck,!1,.2)},u)),setTimeout(()=>{U()},300),setTimeout(()=>{if(d){T(d.dataset.weight),T(l[0].dataset.weight)+1;const r=T(l[0].dataset.weight)===T(d.dataset.weight)-1,u=ee(d.dataset.color,l[0].dataset.color);console.log(!u),console.log(!r),console.log(T(d.dataset.weight)),console.log(T(l[0].dataset.weight)),(!u||!r)&&c!=o.destinationDeck&&H(d,!1)}},300)}else o.type=="toggle"?(console.log("undoing toggle: "+o.type),o.card&&(o.card.forEach((n,t)=>{n.style.marginLeft="",setTimeout(()=>{P(n,o.destinationDeck,!1,.2),H(n,!1)},t*100)}),setTimeout(()=>{U()},600))):o.type=="rerolltoggle"&&([...s.children].reverse().forEach(n=>{c.append(n),H(n,!0),audioReroll.play(),n.style.marginLeft=""}),setTimeout(()=>{U()},200))}};var J=[];function H(o,n){o.classList.toggle("card_closed",!n),o.classList.toggle("facing-up",n),o.draggable=n}function P(o,n,t=!1,i=.3,l=!0){const d=o.getBoundingClientRect(),r=n.lastElementChild;console.log("lastChild"),console.log(r);const u=r?r.getBoundingClientRect():n.getBoundingClientRect();console.log(u),console.log(n.getBoundingClientRect());var m={x:d.x,y:d.y},w={x:u.left,y:u.top};o.style.marginLeft="",o.style.position="",o.style.zIndex="99999999",l||n.appendChild(o),new TWEEN.Tween(m).to(w,i*1e3).easing(TWEEN.Easing.Quadratic.Out).onUpdate(()=>{o.style.left=`${m.x}px`,o.style.top=`${m.y}px`}).onComplete(()=>{l&&n.appendChild(o),o.style.position="initial",o.style.zIndex="initial",o.style.left="",o.style.top="",t&&M(o),f&&B()}).start()}function se(o){if(!F){var n={x:-10},t={x:10};new TWEEN.Tween(n).to(t,100).repeat(3).yoyo(!0).onUpdate(function(){o.style.transform="translateX("+n.x+"px)"}).onComplete(()=>{o.style.transform=""}).start()}}function q(o){let n=0;for(let t=o.children.length-1;t>=0;t--){const i=o.children[t],l={scale:1},d={scale:1.2};new TWEEN.Tween(l).delay(n*50).to(d,100).repeat(1).yoyo(!0).onUpdate(()=>{const r=l.scale;i.style.transform=`scale(${r})`}).onComplete(()=>{i.style.transform=""}).start(),n++}}function j(o){const n={scale:1,rotation:0},t={scale:1.6,rotation:Math.max(-.1,Math.min(.1,Math.random()*(.2- -.2)+-.2))};console.log("tiltandscaleCard"),console.log(t),new TWEEN.Tween(n).to(t,100).easing(TWEEN.Easing.Quadratic.Out).repeat(1).yoyo(!0).onUpdate(()=>{const i=n.scale,l=n.rotation;o.style.transform=`scale(${i}) rotate(${l}rad)`}).onComplete(()=>{o.style.transform=""}).start()}function M(o){o.classList.add("animated-glow"),setTimeout(()=>{o.classList.remove("animated-glow")},500)}function A(o,n){console.log(o);const t=o.querySelector(".PointFly");console.log(t),t.innerHTML="+"+n,t.classList.add("score-fly-animation"),audioScoreFly.play(),setTimeout(()=>{t.classList.remove("score-fly-animation")},800)}V();function V(){requestAnimationFrame(V),TWEEN.update()}function ae(){const o=[1,2,3,4,5,6,7],n=24,t=document.querySelectorAll(".piles>.holder");Z(document.querySelector(".deck_close"),J.wastePile.close);let i=0;audioStartCards.play(),t.forEach((l,d)=>{setTimeout(()=>{Z(l,J.holderPiles[d],!0),i+=o[d],i===52-n&&(console.log("All cards moved."),setTimeout(()=>{B(),f=!0,Y()},600))},d*200)})}function Z(o,n,t=!1){n.forEach((i,l)=>{let d=re(i);t?setTimeout(()=>{P(d,o,!1,.2)},l*40):o.append(d)})}function Y(){try{document.querySelectorAll(".piles>.holder").forEach((o,n)=>{const t=o.children;Array.from(t).forEach((i,l)=>{W(E.holderPiles[n][l],i)})})}catch(o){console.error(o)}try{const o=c.children;Array.from(o).forEach((n,t)=>{W(E.wastePile.open[t],n)})}catch(o){console.error(o)}try{const o=s.children;Array.from(o).forEach((n,t)=>{W(E.wastePile.close[t],n)})}catch(o){console.error(o)}setTimeout(()=>{Y()},10)}function W(o,n){if(o=="X_X"||n.id!="X_X")return;const t=n.querySelector(".loading-overlay"),[i,l]=o.split("_");n.id=o;var d=Q(l);const r=n.querySelector(".card__corner-top-left"),u=n.querySelector(".card__corner-top-right"),m=n.querySelector(".card__center");t.hidden=!0,n.dataset.weight=i,n.dataset.suit=l,n.dataset.color=l=="h"||l=="d"?"r":"b",r.innerHTML=i,u.innerHTML=d,m.innerHTML=d}function re(o){const t=document.querySelector("#card-template").content.cloneNode(!0).firstElementChild,[i,l]=o.split("_");t.id=o;var d=l!="X"?Q(l):"X";document.querySelector(".field");const r=t.querySelector(".card__corner-top-left"),u=t.querySelector(".card__corner-top-right");t.querySelector(".card__corner-bottom-left"),t.querySelector(".card__corner-bottom-right");const m=t.querySelector(".card__center"),w=t.querySelector(".loading-overlay");return t.dataset.weight=i,t.dataset.suit=l,t.dataset.color=l=="h"||l=="d"?"r":"b",t.dataset.foundation=!1,t.dataset.piled=!1,t.style.position="initial",t.style.zIndex="initial",H(t,!1),w.hidden=o!="X_X",r.innerHTML=i=="X"?"":i,u.innerHTML=d=="X"?"":d,m.innerHTML=d=="X"?"":d,de(t),s.appendChild(t),t}function Q(o){var n;switch(o){case"h":n="&hearts;";break;case"d":n="&diams;";break;case"c":n="&clubs;";break;case"s":n="&spades;";break;default:n="X",console.log("switch suit error");break}return n}function ie(o){console.log(o),o.childElementCount!==0&&(H(o.lastElementChild,!0),o.childElementCount)}let F=!1;function le(){F||(F=!0,setInterval(()=>{var o=new Event("click");document.querySelectorAll(".piles>.holder").forEach((n,t)=>{n.childElementCount!=0&&setTimeout(()=>{n.lastElementChild.dispatchEvent(o)},40*t)})},200))}function B(){console.log("openAllLastCards");let o=!1;document.querySelectorAll(".piles>.holder").forEach((t,i)=>{ie(t)});let n=document.querySelectorAll(".card_closed");n.length==0&&s.children.length==0&&c.children.length==0&&(o=!0,le()),console.log("closedCards "+n.length),console.log("deckCloseEl "+s.children.length),console.log("deckOpenEl "+c.children.length),console.log(o)}function K(o){const n=o.lastElementChild,t=[];if(!n)return t;let i=0,l=n;for(;l&&i<3;)t.unshift(l),l=l.previousElementSibling,i++;return t}function U(){const o=K(c);o.forEach((n,t)=>{t===o.length-1?n.style.marginLeft="50px":t===o.length-2?n.style.marginLeft="25px":n.style.marginLeft="0px"}),console.log(c)}let G=!1;document.querySelector(".deck_close").addEventListener("click",ce);function ce(o){if(G)return;G=!0,setTimeout(()=>{G=!1},1e3),s.lastElementChild;const n=K(s),t=K(c);if(n.length===0){console.log("No cards to process."),console.log("reroll"),h("rerolltoggle",null,null),v.send(JSON.stringify({type:"move",gameType:"casual",game:"solitaire",payload:{accessToken:`Bearer ${C}`,id:_,type:"rerolltoggle",f:0,t:0,c:0}})),[...c.children].reverse().forEach(i=>{P(i,s,!1,.2),H(i,!1),audioReroll.play(),i.style.marginLeft=""}),g>0?g==3?s.style.backgroundImage=`url("${oe}")`:g==2?s.style.backgroundImage=`url("${ye}")`:g==1&&(s.style.backgroundImage=`url("${me}")`):s.style.backgroundImage=`url("${_e}")`;return}h("toggle",n,s),v.send(JSON.stringify({type:"move",gameType:"casual",game:"solitaire",payload:{accessToken:`Bearer ${C}`,id:_,type:"toggle",f:0,t:0,c:0}})),n.forEach((i,l)=>{setTimeout(()=>{P(i,c,!1,.2,!0),H(i,!0),audioDeckdrop.play(),setTimeout(()=>{l===n.length-1?i.style.marginLeft="50px":l===n.length-2?i.style.marginLeft="25px":i.style.marginLeft="0px"},300)},l*100)}),t.forEach((i,l)=>{i.style.marginLeft=""})}let z=[];function de(o){o.addEventListener("dragstart",n=>{audioDraw.play();const t=n.target,i=t.parentElement,l=i.classList.contains("holder_pile"),d=[...i.children],r=d.indexOf(t),u=d.splice(r);if(!l&&u.length>1){n.preventDefault();return}var m=document.createElement("div");m.classList.add("holder"),m.append(...u.map(L=>L.cloneNode(!0),o.style.marginLeft="")),console.log(n);let w=n.dataTransfer;w.setDragImage instanceof Function&&(console.log(m),w.setDragImage(m,50,50)),requestAnimationFrame(()=>{u.forEach(L=>L.hidden=!0),m.hidden=!0}),document.body.append(m),z=u.map(L=>L.id);function b(L){L.preventDefault(),L.dataTransfer.dropEffect="move"}function O(L){u.forEach(x=>x.hidden=!1),m.remove(),U(),R()}function R(){document.removeEventListener("dragover",b),o.removeEventListener("dragend",O),audioCancel.play()}document.addEventListener("dragover",b),o.addEventListener("dragend",O)}),o.addEventListener("click",n=>{const t=n.target,i=t.parentNode;if(t!=i.lastElementChild)return;const l=document.querySelectorAll(".holder_suit");let d=!0;[...l].some(r=>{if(!t.draggable)return!1;const u=t.dataset.weight==="A",m=r.childElementCount===0,w=r.lastElementChild,b=r&&r.dataset.suit===t.dataset.suit,O=w&&T(w.dataset.weight)===T(t.dataset.weight)-1;return u&&b&&m?(console.log("MOVE"),P(t,r,!0,.3,!1),j(t),t.dataset.foundation=="false"&&setTimeout(()=>{N=R=>{A(t,R)}},300),h("move",t,i),v.send(JSON.stringify({type:"move",gameType:"casual",game:"solitaire",payload:{accessToken:`Bearer ${C}`,id:_,type:"place",p:i.id=="o"?"w_f":"h_f",f:i.id,t:r.id,c:1}})),t.dataset.foundation=!0,console.log("foundation"),console.log(r),audioAutodrop.play(),d=!1,!0):w&&b&&O?(console.log("MOVE"),P(t,r,!0,.3,!1),j(t),t.dataset.foundation=="false"&&setTimeout(()=>{N=R=>{A(t,R)}},300),h("move",t,i),v.send(JSON.stringify({type:"move",gameType:"casual",game:"solitaire",payload:{accessToken:`Bearer ${C}`,id:_,type:"place",p:i.id=="o"?"w_f":"h_f",f:i.id,t:r.id,c:1}})),t.dataset.foundation=!0,console.log("foundation piled card"),console.log(r),audioAutodrop.play(),d=!1,!0):!1}),d&&t.draggable&&t.dataset.foundation=="false"&&(console.log("SHAKE"),se(t)),B()})}function T(o){var n;switch(o){case"A":n=1;break;case"K":n=13;break;case"Q":n=12;break;case"J":n=11;break;default:n=+o;break}return n}function ee(o,n){return["r"].includes(o)!==["r"].includes(n)}this.init=function(o){J=o,document.querySelectorAll(".holder").forEach(t=>{t.addEventListener("dragover",i=>{i.preventDefault()}),t.addEventListener("drop",i=>{const l=i.target;console.log("--------------------"),console.log("--------------------"),console.log(l),console.log(t),console.log(t.parentNode),console.log(t.parentNode.children),console.log(Array.from(t.parentNode.children)),console.log(Array.from(t.parentNode.children).indexOf(t)),console.log("--------------------"),console.log("--------------------");const d=z.map(D=>document.getElementById(D));z=[];const[r]=d,u=r.parentNode,m=t.classList.contains("holder_pile"),w=t.classList.contains("holder_suit"),b=l.classList.contains("holder"),O=l.classList.contains("card"),R=ee(l.dataset.color,r.dataset.color),L=t==u,x=t.dataset.suit===r.dataset.suit,ue=r.dataset.weight==="K",pe=r.dataset.weight==="A",ge=T(l.dataset.weight)===T(r.dataset.weight)+1,fe=T(l.dataset.weight)===T(r.dataset.weight)-1,he=O?t.lastElementChild==l:!1;console.log(r.dataset.weight),console.log(l.dataset.weight),console.log(x),console.log(d),console.log(t),console.log(t.childElementCount),L||(m&&(b&&ue&&t.childElementCount==0&&(t.append(...d),r.style.marginLeft="",h("move",r,u),v.send(JSON.stringify({type:"move",gameType:"casual",game:"solitaire",payload:{accessToken:`Bearer ${C}`,id:_,type:"place",p:u.id==="h"||u.id==="d"||u.id==="c"||u.id==="s"?"f_h":u.id==="o"?"w_h":"h_h",f:u.id,t:t.id,c:d.length}})),q(t),M(r),N=D=>{A(r,D)},r.dataset.piled=!0,console.log("king piled up"),console.log(t)),O&&R&&ge&&he&&(t.append(...d),r.style.marginLeft="",h("move",r,u),v.send(JSON.stringify({type:"move",gameType:"casual",game:"solitaire",payload:{accessToken:`Bearer ${C}`,id:_,type:"place",p:u.id==="h"||u.id==="d"||u.id==="c"||u.id==="s"?"f_h":u.id==="o"?"w_h":"h_h",f:u.id,t:t.id,c:d.length}})),q(t),M(r),N=D=>{A(r,D)},r.dataset.piled=!0,console.log("piled up"),console.log(t))),w&&d.length===1&&(b&&x&&pe&&(t.append(...d),r.style.marginLeft="",h("move",r,u),v.send(JSON.stringify({type:"move",gameType:"casual",game:"solitaire",payload:{accessToken:`Bearer ${C}`,id:_,type:"place",p:u.id=="o"?"w_f":"h_f",f:u.id,t:t.id,c:d.length}})),q(t),M(r),N=D=>{A(r,D)},r.dataset.foundation=!0,console.log("foundation "+x),console.log(t)),O&&x&&fe&&(t.append(...d),r.style.marginLeft="",h("move",r,u),v.send(JSON.stringify({type:"move",gameType:"casual",game:"solitaire",payload:{accessToken:`Bearer ${C}`,id:_,type:"place",p:u.id=="o"?"w_f":"h_f",f:u.id,t:t.id,c:d.length}})),q(t),M(r),N=D=>{A(r,D)},r.dataset.foundation=!0,console.log("foundation piled card"),console.log(t)))),B()})}),ae()}}console.log("------------------------------");console.log("------------------------------");console.log(sessionStorage.getItem("token"));console.log("------------------------------");console.log("------------------------------");const k=sessionStorage.getItem("currentGamePayload")?JSON.parse(sessionStorage.getItem("currentGamePayload")):{token:"eyJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoicmpkWldxTzhsbUU2IiwidCI6MTcxMzM0NDE1OSwicHRfaWQiOjg0NzksInNsdWciOiJxVnhMMm9rUHNqSkdjSmdzaXpPZHN3In0.L9HanETtlahtCkos6xcOZGmlCww6lopw_n1NIe3-rGo",tournamentData:{id:"BwnxWL",game_id:"pLger8BqNM06",room_id:""}};console.log("arenaData");console.log(k);console.log("arenaData.tournamentData");console.log(k.tournamentData);console.log("arenaData.tournamentData.id");console.log(k.tournamentData.id);console.log("arenaData.tournamentData.game_id");console.log(k.tournamentData.game_id);console.log("arenaData.tournamentData.room_id");console.log(k.tournamentData.room_id);const C=k.token;new URL(import.meta.url);const Ie="wss",He="rp.playzap.games";let Pe=()=>{};function Re(){v.readyState===WebSocket.OPEN&&v.send(JSON.stringify({type:"ping"}))}const Ne=3e4;function Ae(f,S,g){const a=new WebSocket(`${f}://${S}`);let e=!1;return a.addEventListener("open",()=>{e=!0,a.send(JSON.stringify({type:"connection_init",gameType:"casual",game:"solitaire",payload:{accessToken:`Bearer ${C}`,lockdownToken:"s5MNWtjTM5TvCMkAzxov"}})),console.log("Connected to WebSocket server")},{once:!0}),a.addEventListener("message",async({data:s})=>{xe(JSON.parse(s))}),a.addEventListener("close",async({wasClean:s})=>{if(!s){if(!e&&g){g();return}console.log("[vite] server connection lost. polling for restart...")}}),a}var X;let _,E,N=f=>{};const v=Ae(Ie,He,Pe);setInterval(()=>{Re()},Ne);function xe(f){switch(console.log(f),f.type){case"connection_ack":v.send(JSON.stringify({type:"createSession",gameType:"casual",game:"solitaire",payload:{accessToken:`Bearer ${C}`,tournamentData:{tournamentID:k.tournamentData.id,gameID:k.tournamentData.game_id,roomID:k.tournamentData.room_id}}}));break;case"sessionStart":_=f.payload.id,console.log(_),sessionStorage.setItem("sessionID",_),sessionStorage.setItem("currentGameScore",f.payload.score),v.send(JSON.stringify({type:"solitaire_init",gameType:"casual",game:"solitaire",payload:{accessToken:`Bearer ${C}`,id:_}}));break;case"updateSession":E=f.payload;break;case"popUpdate":console.log("popUpdate"),console.log(X.popHistory),E=f.payload,X.popHistory();break;case"scoreUpdate":console.log("scoreUpdate "+f.payload.score),setTimeout(()=>{f.payload.score>0&&N(f.payload.score)},400);break;case"session":E=f.payload,X=new Oe,X.init(f.payload);break}}
