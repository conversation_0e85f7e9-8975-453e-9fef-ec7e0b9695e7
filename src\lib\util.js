export const techColors = {
  Svelte: "#ff3e00", // Svelte's official orange
  Unity: "#222c37", // Unity's dark blue/gray (brand color)
  Blender: "#f57921", // Blender's orange
  HTML5: "#e34f26", // Official HTML5 orange
  "Phaser.js": "#8bb2ff", // Phaser.js light blue from their logo
  "Pixi.js": "#e91e63", // Pixi.js uses a soft blue-purple
  PlayCanvas: "#f68a1e", // PlayCanvas orange from their brand
  "ct.js": "#446adb", // Bright yellow (official site uses bright, playful colors)
  "Node.js": "#8cc84b", // Node.js green
  React: "#61dafb", // React.js blue
  Firebase: "#ffca28", // Firebase yellow (official branding)
  PostgresSQL: "#336791", // PostgresSQL blue
  SvelteKit: "#ff3e00", // SvelteKit's official orange
  jQuery: "#0769ad", // jQuery blue
};

export const projects = [
  {
    title:"Okashi Multiplayer + Backend",
    tech: ["Pixi.js", "SvelteKit", "Node.js"],
    description: `
      A full-stack multiplayer version of the popular Okashi Game with real-time physics and competitive gameplay <br><br>

      <b>1. Multiplayer Architecture:</b> Developed a robust room-based multiplayer system supporting 1v1 matches. 
      Implemented WebSocket communication for real-time game state synchronization and player interactions.<br><br>

      <b>2. Physics Engine Integration:</b> Integrated Matter.js physics engine to create realistic sweet movements and collisions. 
      Implemented precise collision detection for merging same-sized sweets.<br><br>

      <b>3. Game Logic Implementation:</b> Designed and implemented core game mechanics including turn-based gameplay, 
      score tracking based on sweet merges, and game-over conditions when sweets reach the top or a specific sweet (waffle) is created.<br><br>

      <b>4. Frontend Development:</b> Built a responsive and interactive game interface using SvelteKit and Pixi.js for efficient rendering. 
      Implemented intuitive controls for sweet positioning and dropping, with support for both mouse and touch inputs.<br><br>

      <b>5. Backend Infrastructure:</b> Developed a scalable Node.js backend to handle game sessions, player matchmaking, 
      and game state management. Utilized TypeScript for enhanced code reliability and maintainability.<br><br>

      <b>6. Performance Optimization:</b> Implemented efficient data structures and algorithms to ensure smooth gameplay 
      even with complex physics calculations and real-time multiplayer interactions. Utilized pnpm workspaces for optimized 
      package management across frontend and backend.
      `,
    image: ["/okashi-title.png", "/backend-title.png"],
    link: {
      "Live (Okashi)": "https://okashi.vercel.app/",
    },
  },
  {
    title: "Suika Game",
    tech: ["Pixi.js", "ct.js", "SvelteKit", "Firebase"],
    description: `
      Nintendo's Suika Game Variant for PlayZap Games <br> <br>
        <b>1. Game Engine Development:</b> Implemented complex physics-based fruit merging mechanics using Pixi.js for rendering and collision detection. Used ct.js as the primary editor for level design, asset management, and game building.<br> <br>
        <b>2. Cross-Platform Communication:</b> Engineered a robust communication system using JavaScript Message Channels to synchronize game state, player scores, and platform commands between the game instance and the hosting platform in real-time.<br> <br>
        <b>3. Frontend Architecture:</b> Designed and built the game platform using SvelteKit for optimal performance and responsive UI, with component-based architecture for maintainable code. Integrated Firebase for user authentication, data persistence, and real-time updates.<br> <br>
        <b>4. Leaderboard System:</b> Developed a comprehensive leaderboard system initially powered by a Ruby backend, featuring global rankings, friend comparisons, and achievement tracking. Implemented caching strategies for performance optimization.
      `,
    image: ["/suika-title.png"],
    link: {
      "Beta Test": "https://suika-solo.vercel.app",
      "Live Version (Merzi)": "https://arena.playzap.games/games",
      // "Source Code": "https://github.com/PlayZap/suika",
    },
    // video: ["/test.mp4"],
  },
  {
    title: "PlayZap Mobile App",
    tech: ["Unity"],
    description: `
      A crypto gaming app . <br> <br>
        <b>1. Core Development</b>: Contributed to the core architecture of the PlayZap mobile app,
         ensuring smooth performance and reliability for competitive multiplayer gaming.<br> <br>
        <b>2. UI/UX Design</b>: Designed and developed the user interface,
        creating an engaging and user-friendly mobile experience tailored for PlayZap's competitive gaming platform.<br> <br>
        <b>3. API Integration</b>: Led the integration of all APIs,
        ensuring seamless communication between the mobile app and PlayZap’s backend services,
        including authentication, leaderboards, in-game economy, on-chain wallets and economy,
        and more.<br> <br>
        <b>4. Cross-platform Optimization</b>: Ensured the mobile app was optimized for both iOS and Android platforms,
        maintaining high-quality performance across devices.<br> <br>
        <b>5. Web3 Integration</b>: Integrated Web3 technologies like blockchain wallet functionality,
        enabling Play-to-Earn features and secure crypto transactions within the app.<br> <br>
      `,
    image: ["/playzapn-title.png"],
    link: {
      "Live Android":
        "https://play.google.com/store/apps/details?id=com.pzp.playzap",
      "Live iOS":
        "https://apps.apple.com/us/app/playzap-games-pvp-rewards/id1629658754",
    },
  },
  {
    title: "PlayZap Telegram Games",
    tech: ["HTML5", "Node.js", "PostgresSQL"],
    description: `
        A unified platform integrating multiple PlayZap web games for the Telegram Mini Apps ecosystem <br> <br>
        <b>1. Server-Side Game Logic:</b> Developed secure server-side game logic using Node.js to prevent cheating and ensure fair gameplay. Implemented complex game mechanics, scoring systems, and randomization algorithms with proper validation and verification.<br> <br>
        <b>2. Real-time Communication System:</b> Engineered a bidirectional WebSocket communication layer for instant game state updates, multiplayer functionality, and low-latency user interactions. Implemented connection recovery, state synchronization, and data compression for optimal performance.<br> <br>
        <b>3. Database Architecture:</b> Designed and implemented a PostgreSQL database schema optimized for game data storage, user progression tracking, and leaderboard functionality with proper indexing and query optimization.<br> <br>
        <b>4. Telegram Integration:</b> Created a seamless integration with Telegram's Mini Apps platform, implementing Telegram's authentication flow, payment systems, and social features. Built responsive HTML5 frontends that adapt to Telegram's UI constraints while maintaining consistent gameplay experience.<br> <br>
      `,
    image: ["/playzaptg-title.png"],
    link: {
      Snowball: "https://tg.playzap.games/?game=winter",
      "2048 'but' 3D": "https://tg.playzap.games/?game=2048",
      Hypersnake: "https://tg.playzap.games/?game=hypersnake",
      Basketball: "https://tg.playzap.games/?game=basketline",
      SliceMaster: "https://tg.playzap.games/?game=slicemaster",
    },
  },
  {
    title: "Solitaire's Backend + Game",
    tech: ["Node.js", "PostgresSQL", "HTML5", "jQuery"],
    description: `
      A comprehensive Solitaire game with secure backend infrastructure <br> <br>
        <b>1. Game Logic Implementation:</b> Developed sophisticated server-side Solitaire game logic in Node.js including card shuffling algorithms, move validation, scoring systems, and game state management. Implemented precise game timer functionality with millisecond accuracy for competitive play.<br> <br>
        <b>2. Real-time Communication:</b> Engineered a WebSocket-based communication system for instant game state updates, move validation, and timer synchronization between client and server with fallback mechanisms for connection interruptions.<br> <br>
        <b>3. Performance Optimization:</b> Implemented an in-memory caching system for active game sessions to minimize database load and ensure responsive gameplay. Designed efficient data structures for game state representation and serialization.<br><br>
        <b>4. Security Implementation:</b> Created a token-based authentication system that integrates with the PlayZap main platform. Implemented JWT validation, encryption for sensitive data, and protection against common attack vectors.<br><br>
        <b>5. Frontend Development:</b> Built an interactive and responsive Solitaire interface using HTML5 and jQuery with drag-and-drop card movement, animations, and visual feedback. Optimized rendering performance for smooth gameplay across devices.<br><br>
        <b>6. Integration Architecture:</b> Designed the game as a modular component that seamlessly integrates into the PlayZap platform via iFrame with secure cross-frame communication protocols and consistent styling.
      `,
      image: ["/backend-title.png", "/solitaire-title.png"],
      // link: {Game: "/solitaire/index.html"},
      link: {"Live Version (Solitaire Pro)": "https://arena.playzap.games/"},
    },
    {
      title: "Fishing Game + Backend",
      tech: ["Phaser.js", "HTML5", "Node.js", "PostgresSQL"],
      description: `
        A complete fishing game with backend infrastructure inspired by the popular Tiny Fishing game <br> <br>
          <b>1. Game Development:</b> Created an engaging fishing game using Phaser.js for core mechanics including physics-based fishing line dynamics, fish AI behaviors, and underwater environments. Implemented progressive difficulty scaling, upgrade systems, and achievement mechanics.<br><br>
          <b>2. HTML5 Optimization:</b> Optimized the game for web performance using advanced HTML5 techniques including canvas optimization, asset preloading, and efficient sprite management to ensure smooth gameplay across devices.<br><br>
          <b>3. Backend Architecture:</b> Designed and implemented a comprehensive Node.js backend with RESTful APIs for game state management, user progression, and server-side validation to prevent cheating.<br><br>
          <b>4. Database Design:</b> Created a PostgreSQL database schema with proper normalization and indexing for efficient storage and retrieval of player data, game statistics, and leaderboard information.<br><br>
          <b>5. Full-Stack Integration:</b> Engineered seamless communication between frontend and backend components using WebSockets for real-time updates and RESTful APIs for data persistence.
        `,
      image: ["/backend-title.png", "/tinyfish-title.png"],
      link: {
        "Alpha Test": "https://tiny-fishing-plum.vercel.app/",
      },
    },
  {
    title: "Casino Backend",
    tech: ["Node.js", "PostgresSQL", "Svelte"],
    description: `
        A comprehensive casino backend system with blockchain integration <br> <br>
        <b>1. Server Architecture:</b> Designed and implemented a scalable Node.js backend architecture for casino operations with microservice components for game logic, user management, and financial transactions. Utilized Bull for efficient job queuing, task scheduling, and background processing.<br><br>
        <b>2. Game Logic Security:</b> Developed server-side implementations of multiple casino games with provably fair algorithms, random number generation with entropy sources, and comprehensive audit logging to ensure game integrity.<br><br>
        <b>3. Real-time Communication:</b> Engineered a WebSocket infrastructure for real-time game state updates, chat functionality, and multiplayer features with proper connection management, load balancing, and failover mechanisms.<br><br>
        <b>4. User Engagement Systems:</b> Created comprehensive user engagement modules including a referral system with multi-level tracking, weekly leaderboards with dynamic prize pools, and achievement systems to drive retention.<br><br>
        <b>5. Blockchain Integration:</b> Implemented secure blockchain wallet management for users including private key encryption, transaction signing, and balance tracking. Developed withdrawal functionality with multi-level approval workflows and security checks.<br><br>
        <b>6. Token Economics:</b> Designed and implemented on-chain token swapping functionality allowing users to convert between cryptocurrencies and in-game tokens with proper rate calculation, fee management, and transaction verification.<br><br>
        <b>7. Administration Interface:</b> Built a comprehensive Svelte-based admin panel with role-based access control, real-time monitoring dashboards, user management tools, and blockchain operation controls for casino operators.
      `,
    image: ["/backend-title.png"],
    link: {Admin: "https://cc.playzap.games/"},
  },
  {
    title: "Casino Plinko",
    tech: ["HTML5"],
    description: `
        An interactive Plinko casino game with advanced visual effects <br> <br>
        <b>1. Frontend Development:</b> Created an engaging Plinko game interface using HTML5 Canvas with realistic physics simulation, particle effects, and smooth animations. Implemented responsive design principles for optimal gameplay across device sizes.<br> <br>
        <b>2. Physics Implementation:</b> Developed realistic ball physics with proper collision detection, bouncing mechanics, and randomization factors that create an authentic Plinko experience while maintaining fair gameplay.<br> <br>
        <b>3. Real-time Communication:</b> Implemented WebSocket integration for secure server-client communication, ensuring game outcomes are determined server-side to prevent manipulation while maintaining responsive gameplay.<br> <br>
        <b>4. Betting Interface:</b> Designed an intuitive betting system with multiple stake options, risk/reward settings, and clear visual feedback for potential winnings based on drop positions.<br> <br>
        <b>5. Result Visualization:</b> Created engaging win/loss animations and sound effects that enhance the user experience and provide clear feedback on game outcomes.
      `,
    image: ["/plinko-title.png"],
    link: {"Live Test": "https://casino-core-games.vercel.app/#/plinko"},
  },
  {
    title: "Casino Wheel",
    tech: ["HTML5"],
    description: `
        A dynamic casino wheel game with customizable betting options <br> <br>
        <b>1. Wheel Animation:</b> Developed a visually appealing spinning wheel using HTML5 Canvas with smooth rotation physics, dynamic lighting effects, and realistic momentum-based stopping mechanics.<br> <br>
        <b>2. Betting System:</b> Implemented a comprehensive betting interface allowing players to place wagers on multiple wheel segments simultaneously with intuitive controls and clear visual indicators.<br> <br>
        <b>3. Server Integration:</b> Created secure WebSocket communication between client and server ensuring all game outcomes are determined server-side with proper validation and verification to maintain game integrity.<br> <br>
        <b>4. Sound Design:</b> Integrated immersive audio effects synchronized with wheel rotation, segment highlighting, and win/loss outcomes to enhance the gaming experience.<br> <br>
        <b>5. Outcome Visualization:</b> Designed engaging win/loss sequences with animations, particle effects, and dynamic UI updates that clearly communicate results to players while maintaining excitement.
      `,
    image: ["/wheel-title.png"],
    link: {"Live Test": "https://casino-core-games.vercel.app/#/wheel"},
  },
  {
    title: "Casino Hi-Lo",
    tech: ["HTML5"],
    description: `
        A strategic Hi-Lo card prediction game with betting progression <br> <br>
        <b>1. Game Interface:</b> Designed an intuitive Hi-Lo game interface with clear card visualization, betting controls, and game history tracking. Implemented smooth card animations and transitions for revealing new cards.<br> <br>
        <b>2. Betting Progression:</b> Developed a dynamic betting system with multiplier progression that increases potential rewards as players correctly predict consecutive card outcomes, creating engaging risk/reward gameplay.<br> <br>
        <b>3. Probability Display:</b> Implemented real-time probability indicators showing players the mathematical chances of higher/lower outcomes based on the current card and remaining deck composition.<br> <br>
        <b>4. Server Security:</b> Engineered secure WebSocket communication ensuring all card draws and game outcomes are determined server-side with proper randomization and verification to prevent prediction or manipulation.<br> <br>
        <b>5. Game Statistics:</b> Created a comprehensive statistics tracking system showing players their performance history, win rates, and longest winning streaks to enhance engagement.
      `,
    image: ["/hilo-title.png"],
    link: {"Live Test": "https://casino-core-games.vercel.app/#/hilo"},
  },
  {
    title: "Casino Blackjack",
    tech: ["HTML5"],
    description: `
        A feature-rich Blackjack casino game with multiple betting options <br> <br>
        <b>1. Game Rules Implementation:</b> Developed a complete Blackjack game following casino standards including splitting, doubling down, insurance bets, and dealer rules. Implemented proper card counting and hand evaluation algorithms.<br> <br>
        <b>2. Card Animations:</b> Created fluid card dealing, flipping, and hand movement animations using HTML5 and CSS3 techniques for an authentic casino experience. Designed realistic card visuals with proper suit and rank rendering.<br> <br>
        <b>3. Betting Interface:</b> Designed an intuitive chip-based betting system with multiple denomination options, bet adjustment controls, and clear visual feedback for current wagers and potential payouts.<br> <br>
        <b>4. Server Integration:</b> Engineered secure WebSocket communication ensuring all card deals and game outcomes are determined server-side with proper shuffling algorithms and verification to maintain game integrity.
      `,
    image: ["/blackjack-title.png"],
    link: {"Live Test": "https://casino-core-games.vercel.app/#/blackjack"},
  },
  {
    title: "Casino Slots",
    tech: ["HTML5"],
    description: `
        An engaging slot machine game with multiple paylines and bonus features <br> <br>
        <b>1. Reel Mechanics:</b> Developed realistic slot machine reel mechanics with proper spinning physics, symbol alignment, and stopping sequences using HTML5. Implemented variable speed and momentum for authentic slot machine feel.<br> <br>
        <b>2. Symbol Design:</b> Created visually appealing slot symbols with animations for winning combinations and special symbols (wilds, scatters, bonus triggers). Implemented proper symbol weighting and distribution algorithms.<br> <br>
      `,
    image: ["/slots-title.png"],
    link: {"Alpha Test": "https://casino-core-games.vercel.app/#/slots"},
  },
  {
    title: "Doggy Run",
    tech: ["Unity"],
    description:`
      Level based hyper-casual game, where choices made during level decide upgrade Level
      and help player in final boss fight
      <br> <br>
      <b>1. Game Design:</b> Designed and implemented the core gameplay mechanics, including character movement, obstacle avoidance, and power-up collection. Ensured smooth and responsive controls for an engaging experience.
      <br> <br>
      <b>2. Level Design:</b> Predesigned levels are placed and removed based on render distance to ensure a smooth performance. Also, implemented an object pooling design to reduce the performance cost of instantiating and destroying objects. Ensured a balanced progression that challenges players without becoming frustrating.
      <br> <br>
      <b>3. Visual Design:</b> Designed and implemented the game's visual style, including character models, environments, and UI elements. Ensured a cohesive and visually appealing look throughout the game.
      <br> <br>
      <b>4. In-app Purchase:</b> Integrated a custom in-app purchase store for cosmetic items, including hats, collars, and other fun accessories. Implemented a secure and user-friendly purchase process that allows players to buy items with in-game currency or real money.
      <br> <br>
      <b>5. Ad Integration:</b> Integrated ads into the game, including rewarded videos, interstitial ads, and banner ads. Ensured ads are displayed in a non-intrusive manner and do not disrupt the player's experience.

    `,
    image: ["/doggu-title.webp"],
    link: {
      "App Store": "https://apps.apple.com/us/app/doggy-run-3d/id1600090230", 
      Youtube:"https://www.youtube.com/watch?v=M9JvPTTzCtY"
    },
  },
  {
    title: "Hexa Run",
    tech: ["Unity"],
    description:`
      A casual game inspired by the Minecraft minigame TnT Run, where the floor becomes progressively weaker as players continue to stand on the same tile.<br> <br>
      <b>1. Game Mechanics:</b> Developed core mechanics including dynamic floor weakening and player movement. Implemented a tile-based system that tracks player positions and adjusts tile strength in real-time.<br> <br>
      <b>2. Level Design:</b> Designed multiple challenging levels with varying tile durability and layout configurations to test player agility and strategic planning.<br> <br>
      <b>3. Bot Functionality:</b> Implemented AI-controlled players that mimic human behavior, providing an engaging single-player experience and realistic competition.<br> <br>
      <b>4. Randomized Level Progression:</b> Implemented a probability-based level system, where the player progresses through levels based on their performance. Levels now spawn randomly, with each level having a unique set of challenges and rewards.<br> <br>
      <b>5. Power-Ups and Upgrades:</b> Added power-ups and upgrades that spawn randomly on the map. These can be collected by the player to gain temporary advantages such as increased speed, shield, or score multiplier.
      <b>6. Visual Effects:</b> Added visual feedback for tile weakening and breaking, including particle effects and animations to enhance gameplay experience.<br> <br>
      <b>7. User Interface:</b> Developed an intuitive UI displaying player scores, remaining time, and current level status. Ensured the interface was responsive and accessible across devices.
    `,
    image: ["/hex-run-title.png"],
    link: {
    },
  },
  {
    title: "BUNNY'S PLANET RUNNER",
    tech: ["Unity"],
    description:`
      It's a concept of casual infinite runner type game, includes all the core necesarry components needed for an infinite runner game:<br><br>

      <b>1. Tile-based Procedural Level Generation:</b> Developed a system that generates levels procedurally, using tiles as the core component. The system ensures that the level is generated randomly and uniquely every time the game is started.<br><br>
      <b>2. Character Controller:</b> Implemented a character controller that allows the player to move left and right, and also allows them to jump and double jump. The controller also takes care of the player's gravity and collision detection.<br><br>
      <b>3. Obstacle Generation:</b> Created a system that generates obstacles such as platforms, enemies and power-ups. The system ensures that obstacles are spawned at random intervals and at random positions on the level.<br><br>
      <b>4. Score System:</b> Developed a scoring system that rewards the player for collecting power-ups and surviving for a longer time. The system also keeps track of the player's best score and displays it on the game over screen.<br><br>
      <b>5. Game Over Screen:</b> Created a game over screen that displays the player's score and best score. The screen also allows the player to restart the game or return to the main menu.<br><br>
      <b>6. UI Design:</b> Designed a user-friendly UI that displays the player's score, best score and the current level. The UI is also responsive and works well on different devices and screen sizes.
    `,
    image: ["https://static.wixstatic.com/media/04dadc_9cfe2cbb72564ffb9abb6bbe59a2bf97~mv2.jpg/v1/fill/w_711,h_560,fp_0.50_0.50,q_85,usm_0.66_1.00_0.01,enc_avif,quality_auto/04dadc_9cfe2cbb72564ffb9abb6bbe59a2bf97~mv2.jpg"],
    link: {
      Instagram: "https://www.instagram.com/p/CHDU7MDjzMV/"
    },
  },
  {
    title:"Ice Cream Shop",
    tech: ["Unity", "Blender"],
    description:`
      It's a concept of a casual game, which i made as a by-product of learning fluid simulation in Blender. 
      And wanting to make it a component in a game inpired by my childhood favorite game(Purble Place).

      <br> <br>
      <b> 3D Modeling and Fluid Simulation:</b> Modeled all game assets using Blender, 
      focusing on detailed and stylized designs to enhance visual appeal. 
      Implemented fluid simulation techniques for realistic syrup flow, 
      ensuring smooth and convincing movement during gameplay. 
      <br> <br>
      Utilized Blender's physics engine to achieve accurate fluid dynamics and visually engaging effects.
    `,
    image: ["icecream-title.png"],
    link: {
      Instagram: "https://www.instagram.com/p/CLkNK70jgjT/"

    },
  },
  {
    title:"MenAtWork",
    tech: ["Unity"],
    description:`
      A Third Person Multiplayer Battle Royale Game. Player has ability to shoot aura abilities and special mellee attacks.
      <br> <br>

      <b>1. Multiplayer Architecture:</b> Developed a robust multiplayer system using Unity's networking features. 
      Ensured smooth and responsive gameplay for up to 50 players in a match. Implemented a reliable matchmaking system to connect players efficiently.
      <br> <br>

      <b>2. Aura Abilities:</b> Designed and implemented a variety of aura abilities, each with unique effects and cooldowns. 
      Ensured balanced power levels and interesting interactions between abilities.
      <br> <br>

      <b>3. Mellee Attacks:</b> Created a system for special mellee attacks, allowing players to engage in close combat. 
      Implemented hit detection, damage calculation, and knockback effects.
      <br> <br>

      <b>4. UI/UX:</b> Designed an intuitive and responsive user interface for both in-game and matchmaking screens. 
      Ensured a seamless and enjoyable experience for players.
      <br> <br>

      <b>5. Optimization:</b> Implemented performance optimization techniques to ensure smooth gameplay on a variety of devices. 
      Utilized Unity's profiler to identify and address performance bottlenecks.
      <br> <br>

      <b>6. Testing and Balancing:</b> Conducted extensive testing to identify and fix bugs. 
      Balanced the game mechanics to ensure fair and engaging gameplay.
    `,
    image: ["/MenAtWork-title.png"],
    link: {
      "Instagram": "https://www.instagram.com/p/CAlXvgfjL5F/",
      "Instagram (WEAPON TESTING)": "https://www.instagram.com/p/B9E_Lu_HEVD/"
    },
  },
  {
    title: "Cubathon (Infinite Runner)",
    tech: ["Unity"],
    description: `
      An endless runner game with a unique twist, featuring geometric shapes and challenging obstacles <br> <br>

      <b>1. Procedural Level Generation:</b> Implemented an advanced algorithm for creating infinite, 
      unique worlds, ensuring a fresh experience with each playthrough. Optimized the generation process for seamless, 
      lag-free gameplay.<br> <br>

      <b>2. Character Design and Balancing:</b> Designed and implemented 6 distinct cube characters, 
      each with unique abilities and attributes. Carefully balanced these characters to maintain game fairness while 
      offering diverse gameplay styles.<br> <br>

      <b>3. Mission System:</b> Developed a comprehensive career mode featuring over 30 unique missions. 
      Implemented a flexible mission tracking system that adapts to various gameplay elements and player actions.<br> <br>

      <b>4. Visual Themes:</b> Created two distinct world themes with unique visual assets, obstacles, and environmental effects. 
      Implemented a smooth transition system between themes to maintain visual coherence.<br> <br>

      <b>5. Performance Optimization:</b> Utilized advanced Unity optimization techniques including object pooling, 
      LOD systems, and efficient shaders to ensure smooth performance across a wide range of devices.<br> <br>

      <b>6. Analytics Integration:</b> Implemented a robust analytics system to track player behavior, level completion rates,
       and feature usage. Utilized this data to inform ongoing game balance and feature development.
    `,
    image: ["https://img.itch.zone/aW1nLzMyNDY3MzUucG5n/315x250%23c/QXz5GK.png"],
    link: {
      "Live itch.io": "https://hunnyb.itch.io/cubathon",
      "Instagram (Infinite Runner)": "https://www.instagram.com/p/B-uCcHWjwuk/",
      "Instagram (level based)": "https://www.instagram.com/p/B001E_AhLHo/"
    },
  },
  {
    title: "Spiral Leaper", 
    tech: ["Unity", "Blender"],
    description: `
      A challenging and addictive endless descent game through a spiraling tower <br> <br>

      <b>1. Core Gameplay:</b> Developed an innovative gameplay mechanic where players control a bouncing ball descending 
      through a helix tower labyrinth. Implemented precise physics for realistic ball movement and bouncing behavior.<br> <br>

      <b>2. One-tap Controls:</b> Designed an intuitive one-tap control system that's easy to learn but difficult to master,
       allowing players to time their taps for optimal descent and obstacle avoidance.<br> <br>

      <b>3. Procedural Level Generation:</b> Created a dynamic level generation system that produces endless, 
      unique spiral layouts with increasing difficulty as players progress deeper into the tower.<br> <br>

      <b>4. Visual Effects:</b> Implemented rich visual effects including particle systems, dynamic lighting, 
      and camera effects to enhance the sense of speed and depth as players descend through the tower.<br> <br>

      <b>5. Progression System:</b> Developed a player progression system with unlockable ball skins, power-ups, 
      and achievements to increase replay value and long-term engagement.<br> <br>

      <b>6. Performance Optimization:</b> Optimized the game for smooth performance across a wide range of mobile devices, 
      ensuring consistent framerates even with complex physics calculations and visual effects.
    `,
    image: ["https://img.itch.zone/aW1nLzI5ODE1MDUucG5n/315x250%23c/HNhzdi.png"],
    link: {
      "Live itch.io": "https://hunnyb.itch.io/spiral-leaper",
    },
  }
].map((project) => ({ ...project, id: Date.now() + Math.random() }));
