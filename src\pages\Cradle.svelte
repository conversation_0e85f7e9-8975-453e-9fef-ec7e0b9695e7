<script>
  import { onMount } from "svelte";
  import Matter from "matter-js";

  let canvas;

  onMount(() => {
    const {
      Engine,
      Render,
      Runner,
      Composite,
      Bodies,
      Constraint,
      Mouse,
      MouseConstraint,
    } = Matter;

    let engine = Engine.create();
    let world = engine.world;

    let render = Render.create({
      canvas: canvas, // Bind the canvas element here
      engine: engine,
      options: {
        width: 800,
        height: 600,
        // showAngleIndicator: true,
        // showCollisions: true,
      },
    });

    Render.run(render);
    let runner = Runner.create();
    Runner.run(runner, engine);

    Composite.add(world, [
      //   Bodies.rectangle(400, 0, 800, 50, { isStatic: true }),
      Bodies.rectangle(400, 600, 800, 50, { isStatic: true }),
      //   Bodies.rectangle(800, 300, 50, 600, { isStatic: true }),
      //   Bodies.rectangle(0, 300, 50, 600, { isStatic: true }),
    ]);

    function createCar(xx, yy, width, height, wheelSize) {
      let group = Matter.Body.nextGroup(true);
      let wheelBase = 20;
      let wheelAOffset = -width * 0.5 + wheelBase;
      let wheelBOffset = width * 0.5 - wheelBase;
      let wheelYOffset = 0;

      let car = Composite.create({ label: "Car" });
      let body = Bodies.rectangle(xx, yy, width, height, {
        collisionFilter: { group: group },
        chamfer: { radius: height * 0.5 },
        density: 0.0002,
      });

      let wheelA = Bodies.circle(
        xx + wheelAOffset,
        yy + wheelYOffset,
        wheelSize,
        {
          collisionFilter: { group: group },
          friction: 0.8,
        }
      );
      let wheelB = Bodies.circle(
        xx + wheelBOffset,
        yy + wheelYOffset,
        wheelSize,
        {
          collisionFilter: { group: group },
          friction: 0.8,
        }
      );

      let axelA = Constraint.create({
        bodyB: body,
        pointB: { x: wheelAOffset, y: wheelYOffset },
        bodyA: wheelA,
        stiffness: 1,
        length: 0,
      });
      let axelB = Constraint.create({
        bodyB: body,
        pointB: { x: wheelBOffset, y: wheelYOffset },
        bodyA: wheelB,
        stiffness: 1,
        length: 0,
      });

      Composite.addBody(car, body);
      Composite.addBody(car, wheelA);
      Composite.addBody(car, wheelB);
      Composite.addConstraint(car, axelA);
      Composite.addConstraint(car, axelB);

      return car;
    }

    function newtonsCradle(xx, yy, number, size, length) {
      //   var Composite = Matter.Composite,
      //     Constraint = Matter.Constraint,
      //     Bodies = Matter.Bodies;

      var newtonsCradle = Composite.create({ label: "Newtons Cradle" });

      for (var i = 0; i < number; i++) {
        var separation = 1.9,
          circle = Bodies.circle(
            xx + i * (size * separation),
            yy + length,
            size,
            {
              inertia: Infinity,
              restitution: 1,
              friction: 0,
              frictionAir: 0,
              slop: size * 0.02,
            }
          ),
          constraint = Constraint.create({
            pointA: { x: xx + i * (size * separation), y: yy },
            bodyB: circle,
          });

        Composite.addBody(newtonsCradle, circle);
        Composite.addConstraint(newtonsCradle, constraint);
      }

      return newtonsCradle;
    }
    Composite.add(world, newtonsCradle(200, 100, 20, 10, 100));
    // Composite.add(world, createCar(150, 100, 150 * 0.9, 30 * 0.9, 30 * 0.9));
    // Composite.add(world, createCar(350, 300, 150 * 0.8, 30 * 0.8, 30 * 0.8));

    // Composite.add(world, [
    //     Bodies.rectangle(200, 150, 400, 20, {
    //       isStatic: true,
    //       angle: Math.PI * 0.06,
    //       render: { fillStyle: "#060a19" },
    //     }),
    //     Bodies.rectangle(500, 350, 650, 20, {
    //       isStatic: true,
    //       angle: -Math.PI * 0.06,
    //       render: { fillStyle: "#060a19" },
    //     }),
    //     Bodies.rectangle(300, 560, 600, 20, {
    //       isStatic: true,
    //       angle: Math.PI * 0.04,
    //       render: { fillStyle: "#060a19" },
    //     }),
    // ]);

    let mouse = Mouse.create(render.canvas);
    let mouseConstraint = MouseConstraint.create(engine, {
      mouse: mouse,
      constraint: { stiffness: 0.2, render: { visible: false } },
    });

    Composite.add(world, mouseConstraint);
    render.mouse = mouse;
    Render.lookAt(render, { min: { x: 0, y: 0 }, max: { x: 800, y: 600 } });

    return () => {
      Matter.Render.stop(render);
      Matter.Runner.stop(runner);
    };
  });
</script>

<main class="canvas-container">
  <canvas bind:this={canvas}></canvas>
</main>

<style>
  .canvas-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100vh;
    background-color: #282c34;
  }

  canvas {
    border: 2px solid white;
    border-radius: 10px;
  }
</style>
