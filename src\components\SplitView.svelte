<script>
  export let filteredProjects;
  let selectedProject = filteredProjects[0];

  function selectProject(project) {
    selectedProject = project;
  }
</script>

<div class="split-view">
  <div class="project-list">
    {#each filteredProjects as project (project.id)}
      <div class="project-item" on:click={() => selectProject(project)}>
        <h3>{project.title}</h3>
      </div>
    {/each}
  </div>

  <div class="project-details">
    <h2>{selectedProject.title}</h2>
    <p>{selectedProject.description}</p>
    <a href={selectedProject.link} target="_blank">Visit Project</a>
  </div>
</div>

<style>
  .split-view {
    display: flex;
    gap: 2rem;
  }
  .project-list {
    flex: 1;
    background: #f4f4f4;
    padding: 1rem;
  }
  .project-item {
    padding: 1rem;
    cursor: pointer;
    transition: background 0.2s ease;
  }
  .project-item:hover {
    background: #007bff;
    color: white;
  }
  .project-details {
    flex: 2;
    padding: 1rem;
    background: #fff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
</style>
