<script>
  import { onMount, onDestroy } from "svelte";
  import * as THREE from "three";
  import { EffectComposer } from "three/examples/jsm/postprocessing/EffectComposer.js";
  import { RenderPass } from "three/examples/jsm/postprocessing/RenderPass.js";
  import { UnrealBloomPass } from "three/examples/jsm/postprocessing/UnrealBloomPass.js";
  import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader.js";

  let container;

  onMount(() => {
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0x05050a);

    const camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    );
    camera.position.set(-20, 100, 20);
    camera.lookAt(0, 0, 0);

    const renderer = new THREE.WebGLRenderer();
    renderer.setSize(window.innerWidth, window.innerHeight);
    container.appendChild(renderer.domElement);

    let isDragging = false;
    let previousMousePosition = { x: 0, y: 0 };

    const gridXSize = 240;
    const gridZSize = 260;
    const cityBlocks = new Map();
    const loader = new GLTFLoader();
    let cachedModel = null;

    loader.load("low_poly_city.glb", (gltf) => {
      cachedModel = gltf.scene;
      cachedModel.scale.set(2, 2, 2);
      updateCityBlocks();
    });

    window.addEventListener("mousedown", (event) => {
      isDragging = true;
      previousMousePosition.x = event.clientX;
      previousMousePosition.y = event.clientY;
    });

    window.addEventListener("mouseup", () => {
      isDragging = false;
    });

    window.addEventListener("mousemove", (event) => {
      if (!isDragging) return;

      const deltaX = (event.clientX - previousMousePosition.x) * 0.1;
      const deltaZ = (event.clientY - previousMousePosition.y) * 0.1;

      const moveDirection = new THREE.Vector3();
      camera.getWorldDirection(moveDirection);
      moveDirection.y = 0;
      moveDirection.normalize();

      const right = new THREE.Vector3();
      right.crossVectors(camera.up, moveDirection).normalize();

      camera.position.addScaledVector(right, deltaX);
      camera.position.addScaledVector(moveDirection, deltaZ);

      previousMousePosition.x = event.clientX;
      previousMousePosition.y = event.clientY;

      if (performance.now() - lastUpdate > 200) {
        lastUpdate = performance.now();
        updateCityBlocks();
      }
    });

    const ambientLight = new THREE.AmbientLight(0x444488, 2.0);
    scene.add(ambientLight);

    let lastUpdate = 0;
    function updateCityBlocks() {
      const camX = Math.floor(camera.position.x / gridXSize) * gridXSize;
      const camZ = Math.floor(camera.position.z / gridZSize) * gridZSize;

      for (let x = -gridXSize * 2; x <= gridXSize * 2; x += gridXSize) {
        for (let z = -gridZSize * 2; z <= gridZSize * 2; z += gridZSize) {
          const key = `${camX + x},${camZ + z}`;
          if (!cityBlocks.has(key) && cachedModel) {
            loadCityBlock(camX + x, camZ + z, key);
          }
        }
      }

      for (const key of cityBlocks.keys()) {
        const [blockX, blockZ] = key.split(",").map(Number);
        if (
          Math.abs(blockX - camX) > gridXSize * 3 ||
          Math.abs(blockZ - camZ) > gridZSize * 3
        ) {
          scene.remove(cityBlocks.get(key));
          cityBlocks.delete(key);
        }
      }
    }

    function loadCityBlock(x, z, key) {
      const city = cachedModel.clone();
      city.position.set(x, 0, z);
      scene.add(city);
      cityBlocks.set(key, city);
    }

    const composer = new EffectComposer(renderer);
    composer.addPass(new RenderPass(scene, camera));
    const bloomPass = new UnrealBloomPass(
      new THREE.Vector2(window.innerWidth, window.innerHeight),
      0.8,
      0.4,
      0.85
    );
    composer.addPass(bloomPass);

    function animate() {
      requestAnimationFrame(animate);
      composer.render();
    }
    animate();

    window.addEventListener("resize", () => {
      renderer.setSize(window.innerWidth, window.innerHeight);
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
      composer.setSize(window.innerWidth, window.innerHeight);
    });
  });
</script>

<div bind:this={container} style="width: 100vw; height: 100vh;"></div>
