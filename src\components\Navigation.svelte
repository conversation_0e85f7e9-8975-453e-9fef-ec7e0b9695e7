<script>
  import Icon from "@iconify/svelte";
  import { fade, fly } from "svelte/transition";

  let isOpen = false;
  export let items = [];

  // Close menu when clicking outside or on a menu item
  function closeMenu() {
    isOpen = false;
  }
</script>

<nav class="navigation">
  <!-- Desktop Navigation -->
  <div class="nav-menu">
    {#each items as item}
      <a href={item.href} class="nav-link">{item.name}</a>
    {/each}
  </div>

  <!-- Mobile Hamburger Icon -->
  <!-- svelte-ignore a11y-no-static-element-interactions -->
  <div class="hamburger">
    <!-- svelte-ignore a11y-click-events-have-key-events -->
    <span class:open={isOpen} on:click={() => (isOpen = !isOpen)}>
      <Icon
        height="40"
        width="40"
        icon="material-symbols:menu-rounded"
        color="white"
      />
    </span>
  </div>

  <!-- Mobile Menu (Slide-in) -->
  {#if isOpen}
    <!-- Overlay -->
    <!-- svelte-ignore a11y-click-events-have-key-events -->
    <!-- svelte-ignore a11y-no-static-element-interactions -->
    <div
      class="overlay"
      transition:fade={{ duration: 300 }}
      on:click={closeMenu}
    ></div>

    <!-- Menu -->
    <!-- svelte-ignore a11y-click-events-have-key-events -->
    <!-- svelte-ignore a11y-no-static-element-interactions -->
    <div
      class="mobile-menu"
      transition:fly={{ x: 200, duration: 300 }}
      class:show={isOpen}
      on:click={closeMenu}
    >
      {#each items as item}
        <a href={item.href} class="mobile-link">{item.name}</a>
      {/each}
    </div>
  {/if}
</nav>

<style>
  .navigation {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  /* Desktop navigation styles */
  .nav-menu {
    display: flex;
    gap: 2rem;
  }

  .nav-link {
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    color: white;
    transition:
      color 0.3s ease,
      transform 0.3s ease;
    position: relative;
  }

  .nav-link:hover {
    color: var(--text--v1-2);
    transform: translateY(-2px);
  }

  .nav-link::after {
    content: "";
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -4px;
    left: 0;
    background-color: var(--text--v1-2);
    transition: width 0.3s ease;
  }

  .nav-link:hover::after {
    width: 100%;
  }

  /* Hamburger styles */
  .hamburger {
    display: none; /* Hidden by default on desktop */
    position: relative;
    z-index: 100;
  }

  svg {
    cursor: pointer;
    transition: transform 0.3s ease;
  }

  svg.open {
    transform: rotate(90deg);
  }

  /* Mobile menu styles */
  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 90;
  }

  .mobile-menu {
    position: fixed;
    top: 0;
    right: 0;
    width: 200px;
    height: 100vh;
    background-color: var(--secondary-background-color);
    z-index: 100;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 5rem;
    gap: 1.5rem;
    transform: translateX(100%);
    transition: transform 0.3s ease;
  }

  .mobile-menu.show {
    transform: translateX(0);
  }

  .mobile-link {
    font-size: large;
    font-weight: 700;
    text-decoration: none;
    color: white;
    transition: color 0.3s ease;
  }

  .mobile-link:hover {
    color: var(--text--v1-2);
  }

  /* Media query for mobile devices */
  @media (max-width: 768px) {
    .nav-menu {
      display: none; /* Hide regular nav on mobile */
    }

    .hamburger {
      display: block; /* Show hamburger on mobile */
    }
  }
</style>
