<script>
  import { onMount } from "svelte";
  import { techColors } from "../lib/util";
  import FilterButton from "./FilterButton.svelte";
  import Iframe from "./Iframe.svelte";
  import VideoPlayer from "./VideoPlayer.svelte";

  export let project;
  export let handleFilter;

  let cardRef;
  let isVisible = false;
  let arrowActive = false;
  let videoModalOpen = false;

  let cardPosition = { top: 0, left: 0, width: 0, height: 0 };

  // Close details if clicked outside the card
  function handleClickOutside(event) {
    if (cardRef && !cardRef.contains(event.target)) {
      isVisible = false;
    }
  }

  onMount(() => {
    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  });
</script>

<!-- svelte-ignore a11y-click-events-have-key-events -->
<!-- svelte-ignore a11y-no-static-element-interactions -->
<div
  class="card"
  bind:this={cardRef}
  on:click={() => {
    // isVisible = !isVisible;
    // arrowActive = !arrowActive;
    isVisible = true;
    arrowActive = false;
  }}
  on:pointerenter={() => {
    // isVisible = true;
    arrowActive = true;
  }}
  on:pointerleave={() => {
    isVisible = false;
    arrowActive = false;
  }}
>
  <div class="bg-overlay"></div>

  {#if project.image}
    <div class="imageContainer">
      {#each project.image as image}
        <div class="imgBG"></div>

        <!-- svelte-ignore a11y-no-noninteractive-element-interactions -->
        <img
          src={image}
          alt={project.title}
          on:click={(event) => {
            videoModalOpen = true;
            const card = event.currentTarget.getBoundingClientRect();
            cardPosition = {
              top: card.top,
              left: card.left,
              width: 0,
              height: 0,
            };
          }}
        />
      {/each}
    </div>
  {/if}

  <span class="title">{project.title}</span>

  <div class="tech-stack">
    {#each project.tech as item}
      <!-- <span style="--bg-color: {techColors[item]};">{item}</span> -->

      <FilterButton
        tech={item}
        color={techColors[item]}
        customClass="cardTag"
        on:filter={handleFilter}
      />
    {/each}
  </div>

  <!-- Smooth transition for description and links -->
  <div class="details" class:isVisible>
    <p class="text-white scrollbar-hide">
      {@html project.description}
    </p>
    <div class="Links">
      {#each Object.entries(project.link) as [key, value]}
        <a href={value} target="_blank">{key}</a>
      {/each}
    </div>
  </div>

  {#if arrowActive}
    <div class="status">
      <span class="text-white">view details</span>
      <svg
        fill="currentColor"
        viewBox="0 0 64 64"
        class="svg-icon status_lo fill-white"
        style="display: {arrowActive ? 'block' : 'none'};"
      >
        <title></title>
        <path
          d="M32.271 49.763 9.201 26.692l6.928-6.93 16.145 16.145 16.144-16.144 6.93 6.929-23.072 23.07h-.005Z"
        ></path>
      </svg>
    </div>
  {/if}
</div>

{#if videoModalOpen && project.video}
  <VideoPlayer
    url={project.video}
    bind:modalOpen={videoModalOpen}
    bind:cardPosition
  />
{/if}

<style>
  .card {
    position: relative;

    width: clamp(300px, 22vw, 600px);
    /* width: 250px; */
    /* max-width: 300px; */
    /* max-width: max-content; */
    min-height: 350px;

    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    gap: 10px;

    padding: 2rem 1rem;

    /* padding: 1rem; */

    background-color: var(--primary-background-color);

    border-radius: 20px;
    border: 1px solid rgba(0, 0, 0, 0.3);
    box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.6) inset;

    filter: grayscale(100%);

    transition:
      filter 0.5s ease,
      transform 0.5s ease,
      box-shadow 0.5s ease;
  }
  .card:hover {
    cursor: pointer;
    transform: scale(1.05);

    filter: grayscale(0%);

    /* border: 1px solid rgba(0, 0, 0, 0.3); */
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3);
  }
  .card:hover img {
    transform: scale(1.1);
  }
  .card:hover .bg-overlay {
    width: 100%;
    height: 100%;

    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;

    opacity: 0.15;

    background-image: url(/bg.svg);
    /* background-size: auto;
    background-position: left top;
    background-repeat: repeat; */

    border-radius: 10%;

    /* transition: fadeIn 1s ease; */
    animation: fadeIn 0.3s ease;
  }
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 0.15;
    }
  }

  img {
    position: relative;

    width: auto;
    height: 8rem;

    /* opacity: 0.8; */
    padding: 25px;
    /* background-color: rgba(90, 90, 90, 0.1); */

    transition:
      transform 0.5s ease,
      filter 0.5s ease;
  }
  img:hover {
    /* box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3); */
    /* background-color: rgba(255, 255, 255, 0.2); */

    /* border: none; */
    /* box-shadow: none; */
  }

  .imageContainer {
    position: relative;
    overflow: hidden;
    background-color: transparent;

    display: flex;
    justify-content: center;
    align-items: center;

    border-radius: 20px;
    /* border: 2px solid rgba(0, 0, 0, 0.3); */
    box-shadow: 0px 0px 5px 2px rgba(0, 0, 0, 0.6) inset;
  }
  .imgBG {
    position: absolute;
    top: 0;
    left: 0;

    width: 100%;
    height: 100%;

    background-color: var(--primary-background-color);
    border-radius: 20px;

    pointer-events: none; /* Prevent interference with hover events */
    transition: background-color 0.3s ease;

    z-index: -1;
  }

  .title {
    width: 100%;
    /* position: absolute;
    top: -2rem;
    left: 50%;
    transform: translateX(-50%); */

    /* margin: 0.5rem 0; */
    text-align: center;
    font-size: 0.9em;
    font-weight: 700;
    color: rgb(87, 179, 255);
  }

  p {
    max-height: 440px;

    font-size: 0.8rem;
    text-align: left;

    /* white-space: nowrap; */
    overflow-y: scroll;
    text-overflow: ellipsis;

    border-radius: 10px;
    background-color: var(--primary-background-color);

    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0px 0px 4px 2px rgba(0, 0, 0, 0.6) inset;
  }

  p::-webkit-scrollbar {
    /* width: 5px; */
  }
  a {
    background-color: rgba(0, 0, 0, 0.4);
    border-radius: 10px;

    padding: 0.5rem 1rem;

    color: #007bff;
    font-size: 0.8em;
    font-weight: 700;
    text-decoration: none;
  }

  .status {
    position: absolute;
    left: 50%;
    bottom: 7px;

    transform: translateX(-50%);
    opacity: 0.3;
    font-size: 0.8em;
    font-weight: 700;
  }

  .status_lo {
    position: absolute;
    left: 50%;
    bottom: 15px;

    transform: translateX(-50%);

    width: 1.2rem;
    height: 1.2rem;

    /* opacity: 1; */
  }

  .tech-stack {
    display: flex;
    justify-content: center;
    align-items: center;

    flex-wrap: wrap;
    gap: 0.5rem;
  }
  .tech-stack span {
    width: fit-content;
    /* background-color: #007bff; */
    background-color: var(--bg-color);

    border-radius: 5px;
    padding: 0.1rem 0.5rem 0.1rem 0.5rem;
  }

  .Links {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .details {
    max-width: 350px;
    max-height: 0;

    opacity: 0;

    overflow: hidden;
    transition:
      max-height 0.8s cubic-bezier(0.4, 0, 0.2, 1),
      opacity 0.5s ease;
  }

  .details.isVisible {
    max-height: 1000px; /* Enough space to expand the content */
    opacity: 1;
  }

  @media (max-width: 1024px) {
    .card {
      /* width: 90vw;
      min-height: 300px; */
      width: clamp(250px, 22vw, 600px);
    }
  }
</style>
