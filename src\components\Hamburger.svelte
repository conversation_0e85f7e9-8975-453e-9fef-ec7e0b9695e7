<script>
  import { onMount } from "svelte";
  import { fade, fly } from "svelte/transition";

  let isOpen = false;

  export let items = [];

  //   // Close details if clicked outside the card
  //   function handleClickOutside(event) {
  //     isOpen = false;
  //   }

  //   onMount(() => {
  //     document.addEventListener("click", handleClickOutside);
  //     return () => {
  //       document.removeEventListener("click", handleClickOutside);
  //     };
  //   });
</script>

<div class="hamburger">
  <!-- Hamburger SVG -->
  <!-- svelte-ignore a11y-click-events-have-key-events -->
  <!-- svelte-ignore a11y-no-static-element-interactions -->
  <svg
    class:open={isOpen}
    on:click={() => (isOpen = !isOpen)}
    xmlns="http://www.w3.org/2000/svg"
    width="40"
    height="40"
    viewBox="0 0 24 24"
    fill="none"
    stroke="white"
    stroke-width="2"
    stroke-linecap="round"
    stroke-linejoin="round"
  >
    <line x1="3" y1="12" x2="21" y2="12" />
    <line x1="3" y1="6" x2="21" y2="6" />
    <line x1="3" y1="18" x2="21" y2="18" />
  </svg>
</div>
<!-- Menu -->
{#if isOpen}
  <!-- svelte-ignore a11y-click-events-have-key-events -->
  <!-- svelte-ignore a11y-no-static-element-interactions -->
  <div
    class="absolute top-0 left-0 w-full h-full bg-black bg-opacity-50 z-10"
    transition:fade={{ duration: 300 }}
    on:click={() => (isOpen = false)}
  ></div>

  <!-- svelte-ignore a11y-click-events-have-key-events -->
  <!-- svelte-ignore a11y-no-static-element-interactions -->
  <div
    class="menu"
    transition:fly={{ x: 200, duration: 300 }}
    class:show={isOpen}
    on:click={() => (isOpen = false)}
  >
    {#each items as item}
      <!-- svelte-ignore a11y-invalid-attribute -->
      <a href={item.href}>{item.name}</a>
    {/each}
  </div>
{/if}

<style>
  .hamburger {
    position: relative;
    /* top: 60px;
    right: 10px; */
  }
  .menu {
    z-index: 9999;
    position: absolute;
    top: 0;
    right: 0;

    transform: translate(100%, 0);

    width: 200px;
    height: 100vh;

    display: flex;
    align-items: center;
    justify-content: start;

    gap: 1.5rem;

    padding-top: 3rem;
    /* padding: 1rem; */
    flex-direction: column;

    background-color: var(--secondary-background-color);

    /* border-radius: 10px; */

    /* border-bottom-left-radius: 10px; */
    /* border-top-left-radius: 10px; */
    /* border: 1px solid rgba(0, 0, 0, 0.3); */
    /* box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.8); */

    transition: transform 0.3s ease;
  }
  .menu.show {
    /* display: flex; */
    transform: translate(0, 0);
  }

  a {
    font-size: large;
    font-weight: 700;
    text-decoration: none;
    /* color: var(--text--v1-2); */
    color: white;
  }

  /* Optional: Add some styles for animation */
  svg {
    cursor: pointer;
    transition: transform 0.3s ease;
  }

  svg.open {
    transform: rotate(90deg);
  }
</style>
