<script>
  import { onMount, onDestroy } from "svelte";
  import * as THREE from "three";
  import { EffectComposer } from "three/examples/jsm/postprocessing/EffectComposer.js";
  import { RenderPass } from "three/examples/jsm/postprocessing/RenderPass.js";
  import { OrbitControls } from "three/examples/jsm/controls/OrbitControls.js";
  import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader.js";

  let container;
  let renderer;
  let composer;
  let scene;
  let camera;
  let controls;
  let animationFrameId;

  const SKY_COLOR = 0x87ceeb;
  const CAMERA_CONFIG = {
    fov: 60,
    near: 0.1,
    far: 1000,
    position: [-25, 50, 30],
    controls: { min: 10, max: 50 },
  };

  function setupScene() {
    scene = new THREE.Scene();
    scene.background = new THREE.Color(SKY_COLOR);
    // scene.fog = new THREE.Fog(SKY_COLOR, 60, 100);
  }

  function setupCamera() {
    camera = new THREE.PerspectiveCamera(
      CAMERA_CONFIG.fov,
      window.innerWidth / window.innerHeight,
      CAMERA_CONFIG.near,
      CAMERA_CONFIG.far
    );
    camera.position.set(...CAMERA_CONFIG.position);
    camera.lookAt(0, 0, 0);
    // camera.rotation.set(0, 0, 0);
    // camera.rotation.x = -0.8;
    // camera.rotation.y = -0.5;
    // camera.rotation.z = -0.45;
  }

  function setupLights() {
    const directional = new THREE.DirectionalLight(0xffffff, 2);
    directional.position.set(20, 80, 40);
    directional.castShadow = true;

    // Shadow configuration
    const shadowConfig = {
      camera: {
        left: -100,
        right: 100,
        top: 100,
        bottom: -100,
        near: 1,
        far: 200,
      },
      bias: -0.001,
      normalBias: 0.05,
      mapSize: { width: 2048, height: 2048 },
    };

    Object.assign(directional.shadow.camera, shadowConfig.camera);
    directional.shadow.bias = shadowConfig.bias;
    directional.shadow.normalBias = shadowConfig.normalBias;
    directional.shadow.mapSize.set(
      shadowConfig.mapSize.width,
      shadowConfig.mapSize.height
    );

    const ambient = new THREE.AmbientLight(0xffffff, 1);

    scene.add(directional, ambient);
  }

  function setupRenderer() {
    renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2)); // Performance optimization
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    container.appendChild(renderer.domElement);

    composer = new EffectComposer(renderer);
    composer.addPass(new RenderPass(scene, camera));
  }

  function loadModel() {
    const loader = new GLTFLoader();
    loader.load(
      "low_poly_city.glb",
      (gltf) => {
        const model = gltf.scene;
        model.traverse((child) => {
          if (child.isMesh) {
            child.castShadow = true;
            child.receiveShadow = true;
            if (child.material) {
              child.material.shadowSide = THREE.FrontSide;
            }
          }
        });
        model.scale.set(2, 2, 2);
        model.position.set(0, 0, 0);
        model.receiveShadow = true;
        scene.add(model);
      },
      undefined,
      (error) => console.error("Error loading model:", error)
    );
  }

  function handleResize() {
    const width = window.innerWidth;
    const height = window.innerHeight;

    camera.aspect = width / height;
    camera.updateProjectionMatrix();

    renderer.setSize(width, height);
    composer.setSize(width, height);
  }

  function animate() {
    animationFrameId = requestAnimationFrame(animate);
    // controls.update();
    composer.render();
  }

  onMount(() => {
    setupScene();
    setupCamera();
    setupLights();
    setupRenderer();

    // controls = new OrbitControls(camera, container);
    // controls.minDistance = CAMERA_CONFIG.controls.min;
    // controls.maxDistance = CAMERA_CONFIG.controls.max;

    // controls.minPolarAngle = Math.PI / 3;
    // controls.maxPolarAngle = Math.PI / 2.5; // Restrict to 90 degrees (horizon level)

    // // Disable default zoom behavior
    // controls.enableZoom = false;

    // Add custom zoom handler
    container.addEventListener(
      "wheel",
      (event) => {
        event.preventDefault();

        const zoomSpeed = 5.0;
        const delta = -Math.sign(-event.deltaY) * zoomSpeed;

        // Store current horizontal position
        const horizontalDistance = Math.sqrt(
          camera.position.x * camera.position.x +
            camera.position.z * camera.position.z
        );

        // Calculate angle in XZ plane
        const angle = Math.atan2(camera.position.z, camera.position.x);

        // Update only Y position
        camera.position.y = Math.max(
          CAMERA_CONFIG.controls.min,
          Math.min(CAMERA_CONFIG.controls.max, camera.position.y + delta)
        );

        // Maintain horizontal position
        camera.position.x = horizontalDistance * Math.cos(angle);
        camera.position.z = horizontalDistance * Math.sin(angle);

        camera.lookAt(0, 0, 0);
      },
      { passive: false }
    );

    loadModel();
    animate();

    window.addEventListener("resize", handleResize);
  });

  onDestroy(() => {
    window.removeEventListener("resize", handleResize);
    cancelAnimationFrame(animationFrameId);

    // Clean up THREE.js resources
    renderer?.dispose();
    composer?.dispose();
    scene?.traverse((object) => {
      if (object instanceof THREE.Mesh) {
        object.geometry.dispose();
        object.material.dispose();
      }
    });
  });
</script>

<div bind:this={container} class="scene-container" />

<!-- <div bind:this={container} style="width: 100vw; height: 100vh;"></div> -->

<style>
  .scene-container {
    width: 100vw;
    height: 100vh;
  }
</style>
