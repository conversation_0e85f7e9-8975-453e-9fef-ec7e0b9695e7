<script>
  import Card from "./Card.svelte";

  export let filteredProjects;
  export let handleFilter;

  let mouseX = 0;
  let mouseY = 0;
  let circleVisible = false;

  // Update mouse position when the mouse moves
  function handleMouseMove(event) {
    mouseX = event.pageX; // Use pageX for scroll support
    mouseY = event.pageY; // Use pageY for scroll support
  }

  // Make the circle visible when the mouse enters the carousel
  function handleMouseEnter() {
    circleVisible = true;
  }

  // Hide the circle when the mouse leaves the carousel
  function handleMouseLeave() {
    circleVisible = false;
  }
</script>

<!-- svelte-ignore a11y-no-static-element-interactions -->
<div
  class="carousel"
  on:mousemove={handleMouseMove}
  on:mouseenter={handleMouseEnter}
  on:mouseleave={handleMouseLeave}
>
  {#each filteredProjects as project (project.id)}
    <Card {project} {handleFilter} />
  {/each}

  <div
    class="highlight {circleVisible ? 'visible' : 'hidden'}"
    style="top: {mouseY}px; left: {mouseX}px;"
  ></div>
</div>

<style>
  .carousel {
    /* width: 100vw; */

    display: flex;
    align-items: start;
    justify-content: center;

    padding: 1rem 0rem;

    flex-wrap: wrap;
    /* overflow-x: scroll; */
    gap: 1rem;
    /* row-gap: 3rem; */
  }

  .highlight {
    position: absolute;
    width: 400px;
    height: 400px;
    transform: translate(-50%, -50%);

    background-color: rgba(255, 255, 255, 0.3); /* Yellow semi-transparent */
    border-radius: 50%;

    filter: blur(50px);
    pointer-events: none; /* Allows mouse events to pass through */
    transition: opacity 0.2s ease-in-out;
    z-index: -1;
  }

  .hidden {
    opacity: 0;
  }

  .visible {
    opacity: 1;
  }
</style>
