<script>
  import ProjectCarousel from "../components/ProjectCarousel.svelte";
  // import SplitView from "./components/SplitView.svelte";
  import FilterBar from "../components/FilterBar.svelte";
  import { projects } from "../lib/util";

  let filteredProjects = [...projects];
  let activeFilters = [];

  function handleFilter(filter) {
    const tech = filter.detail;

    // Check if the filter is already active
    const filterIndex = activeFilters.indexOf(tech);

    if (filterIndex === -1) {
      // Add filter if not already active
      activeFilters = [...activeFilters, tech];
    } else {
      // Remove filter if already active
      activeFilters = activeFilters.filter((item) => item !== tech);
    }

    // Apply all active filters
    if (activeFilters.length === 0) {
      // If no filters are active, show all projects
      filteredProjects = [...projects];
    } else {
      // Filter projects that have at least one of the active filters
      filteredProjects = projects.filter((project) =>
        activeFilters.some((filter) => project.tech.includes(filter))
      );
    }
  }

  function removeFilter(tech) {
    // Remove the filter
    activeFilters = activeFilters.filter((item) => item !== tech);

    // Update filtered projects
    if (activeFilters.length === 0) {
      filteredProjects = [...projects];
    } else {
      filteredProjects = projects.filter((project) =>
        activeFilters.some((filter) => project.tech.includes(filter))
      );
    }
  }
</script>

<!-- Filtering Bar -->
<FilterBar {handleFilter} {removeFilter} {activeFilters} />

<!-- Interactive Project Cards (Carousel) -->
<ProjectCarousel {handleFilter} {filteredProjects} />

<!-- Split View with Project Details -->
<!-- <SplitView {filteredProjects} /> -->
