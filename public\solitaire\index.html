<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>Solitaire</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta charset="utf-8" />
    <link rel="icon" type="image/x-icon" href="/dist/assets/favicon-DtWHycWJ.png" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@900&display=swap"
      rel="stylesheet"
    />

    <script src="https://cdnjs.cloudflare.com/ajax/libs/tween.js/20.0.0/tween.umd.js"></script>

    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.9.1/gsap.min.js"></script> -->

    <!-- <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script> -->
    <!-- <script defer src="/main.js"></script> -->


    <script type="module" crossorigin src="/dist/assets/index-Bln0H18Y.js"></script>
    <link rel="stylesheet" crossorigin href="/dist/assets/index-C_PTt3qe.css">
  </head>
  <body oncontextmenu="return false" class="disableSelection">
    <audio id="audioBG"></audio>
    <audio id="audioWin"></audio>
    <audio id="audioStartCards"></audio>
    <audio id="audioCardFlip"></audio>
    <audio id="audioScoreFly"></audio>
    <audio id="audioDraw"></audio>
    <audio id="audioCancel"></audio>
    <audio id="audioAutodrop"></audio>
    <audio id="audioDeckdrop"></audio>
    <audio id="audioReroll"></audio>
    <template id="card-template">
      <div class="card">
        <div class="image-box__overlay"></div>
        <div class="card__corner-top">
          <div class="card__corner-top-left"></div>
          <div class="card__corner-top-right"></div>
        </div>
        <div class="card__center"></div>

        <div class="PointFly">+14</div>
        <div class="loading-overlay">
          <div class="loading-spinner"></div>
        </div>

        <!-- <div class="card__corner-bottom">
          <div class="card__corner-bottom-left">A</div>
          <div class="card__corner-bottom-right">♠</div>
        </div> -->
      </div>
    </template>
    <div class="bodyPattern"></div>

    <!-- <div class="bodyTop"></div> -->
    <div class="field-container">
      <div class="field">
        <div class="HUD">
          <button class="submitnquit sexyBG">Submit and Quit</button>
          <button class="undo sexyBG">
            <p>5</p>
            <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAYAAACohjseAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAN5SURBVHgB7ZlbSBVRFIb/E3ZBKhWVyJJMiooESyIqyoKIXoJ6UB+6GElkBJIRFEHQowRhRVg9FEVUUL3lQ0HYhQIDCaLUUkpJFLvQ1ZIydfVP89DRUtee2XOOwXzwMRyd/a/Z5+y57D1ASEhISIh3IogBIjKNm3w6n+bQDJpEx9Bu+po200e0nj6ORCK9GM2wU1m0nNaJOS30CF2K0QYPKoOepG/FP/20mi5EvOFBTKB76GexTx89T6cjHrBwEr0hwdNKVyGWiHuudUjscH7NzYgFLLSIfpfY84OWIUhYYLa4QyaebEMQMHgKbZT484EuGe5YPd3oGXqKm50wo5/epDX0Fe2kfTSVZtM8uo6mw4wGupwPBp9gA3Zug5jh3DYqxH2aGSk7hW6kL8WMY7ABg8bSJoPCD+hMGMI2afSwuDd6Dd9oDvzCkELRUwWfMGO7Qb0r8AMDIvSpsthZWIJZW2ivoqbza2fDK2ycLzpqaTIswrwTytoH4RU2rlQUcG76K2EZZibSZ4r6TfCK6C4uFxEQzC5V1HeGaSpMEfdhWsNqBASzJ9NOxTFsim43Rpk/R7FPK72HgOCN/As3DxW75kV/0HZwrmKfhhgsM9xS7DPgWBMG/5c/cRY3Ewb9OQ8j48zg//VFtLHj3bBDnWKfoSfFPMAD4s63bNJG02AB5sxQ1GuMbjN4iJZDP2y1ZEI3AjRojq1/uAYVsE8H3OVAG2hmGj3RHwZ38Ay9Bns4xQp5Dr6HHeYp9nkX/WFAB3kgX7nZS9tgh/3MrIU91ir2eT7iHjxRl9Fu8ccFWIR540U3TyzQBu4W77wQ+w/cJcraU01Cr4o5XXQxLCLuonK9onaLaXC6mC8ulcIyopvJOByCKeKufXYpC5yGZZhZTHsUtZ2ZRCa8wIZligJP6ERYhHlFor/YVcMPDLg0TPhHOgsWYd5R+lP0LIAfGDCJNg8RXgwLMCeBrhf3KmxCJWzgfEvy9/l4HB5h22SaSwtolbgjwZR2mgJbMGyr/Hnhcp+Og0fYtkb84Vx8VsA2DF1Dd9FE+IDtG8Q7TudKMJrx2cF9GO147KBzv9uhrZGA/wvnDVIRZyiatZnf2J69B4XQyzTXpHNxRzFEnav1HQlwvTVQhumg807xnLjvQ3yNsnifg9fhniZv4C4ct9O79DaHoiAkJCQkJM78Asvo9PPZMaptAAAAAElFTkSuQmCC" alt="" width="40px" height="40px" />
          </button>
        </div>
        <div class="field__top">
          <!-- todo(vmyshko): remove redundant classes if possible -->
          <div class="suits">
            <div
              class="holder holder_suit holder_suit_heart"
              data-suit="h"
              id="h"
            ></div>
            <div
              class="holder holder_suit holder_suit_diamond"
              data-suit="d"
              id="d"
            ></div>
            <div
              class="holder holder_suit holder_suit_club"
              data-suit="c"
              id="c"
            ></div>
            <div
              class="holder holder_suit holder_suit_spade"
              data-suit="s"
              id="s"
            ></div>
          </div>
          <div class="decks sexyBG">
            <div class="holder deck_open" id="o"></div>
            <div class="holder deck_close" id="c"></div>
          </div>
        </div>

        <div class="piles">
          <!-- <div class="holder_divider"></div> -->
          <div class="holder holder_pile holder_pile_E" id="0"></div>
          <div class="holder_divider"></div>
          <div class="holder holder_pile holder_pile_O" id="1"></div>
          <div class="holder_divider"></div>
          <div class="holder holder_pile holder_pile_E" id="2"></div>
          <div class="holder_divider"></div>
          <div class="holder holder_pile holder_pile_O" id="3"></div>
          <div class="holder_divider"></div>
          <div class="holder holder_pile holder_pile_E" id="4"></div>
          <div class="holder_divider"></div>
          <div class="holder holder_pile holder_pile_O" id="5"></div>
          <div class="holder_divider"></div>
          <div class="holder holder_pile holder_pile_E" id="6"></div>
          <!-- <div class="holder_divider"></div> -->
        </div>
      </div>
    </div>
  </body>
</html>
